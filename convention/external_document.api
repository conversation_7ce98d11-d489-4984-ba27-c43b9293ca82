syntax = "v1"

type CreateExternalDocumentReq {
    Name string `json:"name"`
    EnglishName string `json:"englishName,optional"` // 0.4版本新增：文件英文名称
    FileID string `json:"fileId,optional"`
    OriginalNumber string `json:"originalNumber,optional"`
    OriginalVersion string `json:"originalVersion,optional"`
    TypeDictionaryNodeId string `json:"typeDictionaryNodeId"`
    DomainDictionaryNodeId string `json:"domainDictionaryNodeId"`
    AuthenticationDictionaryNodeIds []string `json:"authenticationDictionaryNodeIds,optional"`
    OriginalDocNumber string `json:"originalDocNumber"`
    PublishDocNumber string  `json:"publishDocNumber"`
    PublishDepartment string `json:"publishDepartment"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    ReplacementDocID string `json:"replacementDocId,optional"` // 0.5版本新增：替代文件ID
    Remark string `json:"remark,optional"` // 0.5版本新增：备注
    OrgType int `json:"orgType"`
}

type CreateExternalDocumentResp {

}



type GetExternalDocumentReq {
    DistributeID string `form:"id"`
}



type ChangeExternalDocumentReq {
    ID string `json:"id"`
    Name string `json:"name"`
    EnglishName string `json:"englishName,optional"` // 0.4版本新增：文件英文名称
    OriginalDocNumber string `json:"originalDocNumber"`
    PublishDocNumber string `json:"publishDocNumber"`
    PublishDepartment string `json:"publishDepartment"`
    TypeDictionaryNodeId string `json:"typeDictionaryNodeId"`
    DomainDictionaryNodeId string `json:"domainDictionaryNodeId"`
    AuthenticationDictionaryNodeIds []string `json:"authenticationDictionaryNodeIds,optional"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    OriginalNumber string `json:"originalNumber,optional"`
    OriginalVersion string `json:"originalVersion,optional"`
    FileId string `json:"fileId,optional"`
    ReplacementDocID string `json:"replacementDocId,optional"` // 0.5版本新增：替代文档ID
    Remark string `json:"remark,optional"` // 0.5版本新增：备注
    OrgType int `json:"orgType"`
}

type ChangeExternalDocumentResp {

}



type GetExternalDocumentsReq {
    PageInfo
    Number string `json:"number,optional"`
    Name string `json:"name,optional"`
    OriginalNumber string `json:"originalNumber,optional"`
    OriginalDocNumber string `json:"originalDocNumber,optional"`
    PublishDocNumber string `json:"publishDocNumber,optional"`
    PublishDepartment string `json:"publishDepartment,optional"`
    TypeDictionaryNodeIds []string `json:"typeDictionaryNodeIds,optional"`
    DomainDictionaryNodeId string `json:"domainDictionaryNodeId,optional"`
    AuthenticationDictionaryNodeId string `json:"authenticationDictionaryNodeId,optional"`
    BeAttachedFile int8 `json:"beAttachedFile,optional"`
    Status int `json:"status,optional"`
    OrgType int `json:"orgType,optional"`
}

type GetExternalDocumentsResp {
    PageInfo
    Data []ExternalDocumentInfo `json:"data"`
}

type ExternalDocumentInfo {
    ID string `json:"id"`
    Number string `json:"number"`
    Version string `json:"version"`
    OriginalNumber string `json:"originalNumber"`
    OriginalVersion string `json:"originalVersion"`
    Name string `json:"name"`
    EnglishName string `json:"englishName"` // 0.4版本新增：文件英文名称
    DocType string `json:"docType"`
    Domain string `json:"domain"`
    OriginalDocNumber string `json:"originalDocNumber"`
    PublishDocNumber string `json:"publishDocNumber"`
    PublishDepartment string `json:"publishDepartment"`
    ReplacementDocName string `json:"replacementDocName"` // 0.5版本调整：替代文档名称
    ReplacementDocVersion string `json:"replacementDocVersion"` // 0.5版本调整：替代文档版本
    Remark string `json:"remark"` // 0.5版本新增：备注
    ApprovalInfo ApprovalInfo `json:"approvalInfo"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    Authentication string `json:"authentication"`
    Status int `json:"status"`
    TypeDictionaryNodeId string `json:"typeDictionaryNodeId"`
    DomainDictionaryNodeId string `json:"domainDictionaryNodeId"`
    AuthenticationDictionaryNodeIds []string `json:"authenticationDictionaryNodeIds"`
    FileInfo FileInfo `json:"fileInfo"`
}

type PlagiarismCheckReq {
    Ids []string `json:"ids"`
}

type PlagiarismCheckResp {

}

// ==================== 外部文件变更记录相关接口定义 ====================

// 获取外部文件变更记录请求
type GetExternalDocumentChangeRecordsReq {
  DocumentId string `form:"documentId"` // 文档ID（必需）
  PageInfo
}

// 获取外部文件变更记录响应
type GetExternalDocumentChangeRecordsResp {
  Data []ExternalDocumentChangeRecord `json:"data"` // 变更记录列表
  PageInfo
}

// 外部文件变更记录
type ExternalDocumentChangeRecord {
  DocumentNo string `json:"documentNo"` // 集团文件编号
  DocumentVersion string `json:"documentVersion"` // 版本/版次
  OriginalNo string `json:"originalNo"` // 原文件编号
  OriginalVersion string `json:"originalVersion"` // 原版本/版次
  DocumentName string `json:"documentName"` // 文件名称
  EnglishName string `json:"englishName"` // 0.4版本新增：文件英文名称
  DocumentCategory string `json:"documentCategory"` // 文件类别
  Domain string `json:"domain"` // 所属领域
  OriginalDocNumber string `json:"originalDocNumber"` // 原文件号
  PublishDocNumber string `json:"publishDocNumber"` // 发文号
  PublishDepartment string `json:"publishDepartment"` // 发文部门
  ReplacementDocName string `json:"replacementDocName"` // 0.5版本调整：替代文档名称
  ReplacementDocVersion string `json:"replacementDocVersion"` // 0.5版本调整：替代文档版本
  Remark string `json:"remark"` // 0.5版本新增：备注
  Auditors []Approval `json:"auditors"` // 审核人列表（姓名+日期）
  Approvers []Approval `json:"approvers"` // 批准人列表（姓名+日期）
  PublishDate int64 `json:"publishDate"` // 发布日期（毫秒级时间戳）
  EffectiveDate int64 `json:"effectiveDate"` // 实施日期（毫秒级时间戳）
  Authentications []string `json:"authentications"` // 认证方式
  OperationType int `json:"operationType"` // 操作类型（1-新增、2-修订、3-作废）
  UpdatedAt int64 `json:"updatedAt"` // 更新日期（毫秒级时间戳，用于排序）
}


// ==================== 外部作废文档库相关类型定义 ====================

// 获取外部作废文档库列表请求
type GetExternalDeprecatedDocumentsReq {
  PageInfo
  Number string `json:"number,optional"` // 文档编号
  Name string `json:"name,optional"` // 文档名称
  OriginalNumber string `json:"originalNumber,optional"` // 原文档编号
  TypeDictionaryNodeIds []string `json:"typeDictionaryNodeIds,optional"` // 类型字典节点ID
  PublishDepartment string `json:"publishDepartment,optional"` // 发文部门
  Status int32 `json:"status,optional"` // 状态：1即将作废 | 2即将实施 | 3有效 | 4拟修订
  BeAttachedFile int8 `json:"beAttachedFile,optional"` // 是否有附件：0全部 | 1是 | 2否
  OrgType int `json:"orgType,optional"` // 机构类型
}

// 获取外部作废文档库列表响应
type GetExternalDeprecatedDocumentsResp {
  Total int64 `json:"total"` // 总数
  Data []ExternalDeprecatedDocument `json:"data"` // 外部作废文档列表
}

// 外部作废文档信息
type ExternalDeprecatedDocument {
  ID string `json:"id"` // 文档ID
  Number string `json:"number"` // 文档编号
  Name string `json:"name"` // 文档名称
  DocType string `json:"docType"` // 文档类型
  FirstPublishDate int64 `json:"firstPublishDate"` // 首次发布日期
  FirstEffectiveDate int64 `json:"firstEffectiveDate"` // 首次实施日期
  LastDeprecatedDate int64 `json:"lastDeprecatedDate"` // 最后作废日期
  DeprecatedVersionCount int32 `json:"deprecatedVersionCount"` // 作废版本数
}

// 获取外部作废文档详情请求
type GetExternalDeprecatedDocumentDetailReq {
  ID string `form:"id"` // 文档ID
}

// 获取外部作废文档详情响应
type GetExternalDeprecatedDocumentDetailResp {
  ID string `json:"id"` // 文档ID
  Number string `json:"number"` // 文档编号
  Name string `json:"name"` // 文档名称
  DocType string `json:"docType"` // 文档类型
  FirstPublishDate int64 `json:"firstPublishDate"` // 首次发布日期
  FirstEffectiveDate int64 `json:"firstEffectiveDate"` // 首次实施日期
  LastDeprecatedDate int64 `json:"lastDeprecatedDate"` // 最后作废日期
  DeprecatedList []ExternalDeprecatedVersionDetail `json:"deprecatedList"` // 作废清单详情
}

// 外部作废版本详情
type ExternalDeprecatedVersionDetail {
  ID string `json:"id"` // 版本ID
  Number string `json:"number"` // 文档编号
  Version string `json:"version"` // 版本/版次
  OriginalNumber string `json:"originalNumber"` // 原文档编号
  OriginalVersion string `json:"originalVersion"` // 原版本/版次
  Name string `json:"name"` // 文档名称
  EnglishName string `json:"englishName"` // 文档英文名称
  DocType string `json:"docType"` // 文档类型
  Domain string `json:"domain"` // 所属领域
  OriginalDocNumber string `json:"originalDocNumber"` // 原文件号
  PublishDocNumber string `json:"publishDocNumber"` // 发文号
  PublishDepartment string `json:"publishDepartment"` // 发文部门
  ReplacementDocName string `json:"replacementDocName"` // 被替代文档名称
  ReplacementDocVersion string `json:"replacementDocVersion"` // 被替代版本版次
  Remark string `json:"remark"` // 备注
  Auditors []Approval `json:"auditors"` // 审核人列表
  Approvers []Approval `json:"approvers"` // 批准人列表
  PublishDate int64 `json:"publishDate"` // 发布日期
  EffectiveDate int64 `json:"effectiveDate"` // 实施日期
  Authentications []string `json:"authentications"` // 认证方式
  ApprovalID string `json:"approvalId"` // 审批记录ID
}

// ==================== 通过编号和名称查询相关类型定义 ====================

type GetExternalDocumentByNumberAndNameReq {
    Number string `form:"number,optional"`
    Name string `form:"name,optional"`
}

type GetExternalDocumentByNumberAndNameResp {
    Data []ExternalDocInfo `json:"data"`
}

type ExternalDocInfo {
    ID string `json:"id"`
    Number string `json:"number"`
    Name string `json:"name"`
}
