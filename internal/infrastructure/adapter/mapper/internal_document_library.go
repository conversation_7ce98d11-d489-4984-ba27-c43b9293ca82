package mapper

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameInternalDocumentLibrary = "internal_document_library"
)

type InternalDocumentLibraryClient struct {
	db *gorm.DB
}

func NewInternalDocumentLibraryClient(db *DocvaultDB) *InternalDocumentLibraryClient {
	return &InternalDocumentLibraryClient{db: db.GetDB()}
}

func (c *InternalDocumentLibraryClient) GetByID(ctx context.Context, id string) (InternalDocumentLibrary, error) {
	internalDocument := InternalDocumentLibrary{}
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&internalDocument).Error; err != nil {
		return InternalDocumentLibrary{}, err
	}
	return internalDocument, nil
}

type InternalDocumentLibraryPageReq struct {
	Page           int
	PageSize       int
	NoPage         bool
	Ids            []string
	DocCategoryIds []string
	DepartmentIds  []string
	Status         int32
	OrganizationID string
	HasAttachment  int32
	Name           string
	No             string
	OriginalNo     string
}

// InternalDocumentLibrary 是 internal_document_library 表的GORM模型
type InternalDocumentLibrary struct {
	ID                    string         `gorm:"type:varchar(64);primary_key"`
	MainID                string         `gorm:"type:varchar(64);column:main_id"`              // 自关联ID 仅在有历史版本时有值
	OrganizationID        string         `gorm:"type:varchar(64);column:organization_id"`      // 子公司ID
	NoPrefix              string         `gorm:"type:varchar(128);column:no_prefix"`           // 编号前缀
	No                    string         `gorm:"type:varchar(128);column:no"`                  // 编号
	SerialNo              int            `gorm:"type:int(11);column:serial_no"`                // 编号序列号
	Name                  string         `gorm:"type:varchar(255);column:name"`                // 文件名称
	EnglishName           string         `gorm:"type:varchar(255);column:english_name"`        // 文件英文名称
	FileID                string         `gorm:"type:varchar(255);column:file_id"`             // 文件id
	DocCategoryID         string         `gorm:"type:varchar(64);column:doc_category_id"`      // 类别ID
	DepartmentIDs         string         `gorm:"type:varchar(500);column:department_ids"`      // 编制部门ID
	AuthorIDs             string         `gorm:"type:varchar(500);column:author_ids"`          // 编制人ID
	Status                int8           `gorm:"type:tinyint;column:status"`                   // 状态 1- 即将作废 2- 即将实施 3- 有效 4- 拟修订 -1- 作废
	ApprovalInfo          datatypes.JSON `gorm:"type:json;column:approval_info"`               // 审批信息
	VersionNo             int            `gorm:"type:int(11);column:version_no"`               // 版本号
	Version               string         `gorm:"type:varchar(255);column:version"`             // 版本
	PublishDate           time.Time      `gorm:"type:date;column:publish_date"`                // 发布日期
	EffectiveDate         time.Time      `gorm:"type:date;column:effective_date"`              // 实施日期
	OriginalNo            string         `gorm:"type:varchar(128);column:original_no"`         // 原文件编号
	OriginalVersionNo     string         `gorm:"type:varchar(128);column:original_version_no"` // 原文件版本
	Remark                string         `gorm:"type:varchar(500);column:remark"`              // 备注
	OperationType         int8           `gorm:"type:tinyint;column:operation_type"`           // 操作类型 0-无操作 1- 新增,2- 修订,3-作废 仅在有历史版本时有值
	CreatedAt             time.Time      `gorm:"column:created_at"`                            // 创建时间
	UpdatedAt             time.Time      `gorm:"column:updated_at"`                            // 更新时间
	CreatedBy             string         `gorm:"type:varchar(64);column:created_by"`           // 创建人
	UpdatedBy             string         `gorm:"type:varchar(64);column:updated_by"`           // 更新人
	TenantID              string         `gorm:"type:varchar(64);column:tenant_id"`
	ReplacementDocName    string         `gorm:"type:varchar(255);column:replacement_doc_name"`    // 替代文件名称
	ReplacementDocVersion string         `gorm:"type:varchar(255);column:replacement_doc_version"` // 替代版本版次
}

// TableName 指定GORM模型对应的表名
func (InternalDocumentLibrary) TableName() string {
	return TableNameInternalDocumentLibrary
}

type ApprovalInfo struct {
	Auditors  []ApprovalItem `json:"auditors"`  // 审核人
	Approvers []ApprovalItem `json:"approvers"` // 批准人
}

type ApprovalItem struct {
	UserID     string `json:"userId"`
	PassedDate int64  `json:"passedDate"`
}

func (i InternalDocumentLibrary) GetApprovalInfo() ApprovalInfo {
	approvalInfo, err := i.ApprovalInfo.MarshalJSON()
	if err != nil {
		return ApprovalInfo{}
	}
	approvalInfoValue := ApprovalInfo{}
	if err := json.Unmarshal(approvalInfo, &approvalInfoValue); err != nil {
		return ApprovalInfo{}
	}
	return approvalInfoValue
}

func (i *InternalDocumentLibrary) SetApprovalInfo(approvalInfo ApprovalInfo) {
	approvalInfoBytes, err := json.Marshal(approvalInfo)
	if err != nil {
		return
	}
	i.ApprovalInfo = datatypes.JSON(approvalInfoBytes)
}

// GetDepartmentIDs 获取编制部门ID列表
// 功能：将顿号分隔的编制部门ID字符串转换为切片
// 返回值：编制部门ID切片
func (i InternalDocumentLibrary) GetDepartmentIDs() []string {
	if i.DepartmentIDs == "" {
		return []string{}
	}
	return strings.Split(i.DepartmentIDs, "、")
}

// BatchGetDocumentNames 批量根据文档ID和版本号查询文档名称
// 功能: 根据文档ID和版本号列表批量查询文档名称
// 参数:
//   - ctx: 上下文
//   - docIDVersions: 文档ID和版本号的映射
//
// 返回值:
//   - map[string]string: 文档ID+版本号为key，文档名称为value的映射
//   - error: 错误信息
func (c *InternalDocumentLibraryClient) BatchGetDocumentNames(ctx context.Context, docIDVersions map[string]string) (map[string]string, error) {
	if len(docIDVersions) == 0 {
		return make(map[string]string), nil
	}

	// 构建查询条件
	var conditions []string
	var args []interface{}
	for docID, versionNo := range docIDVersions {
		conditions = append(conditions, "(id = ? AND version_no = ?)")
		args = append(args, docID, versionNo)
	}

	var docs []struct {
		ID        string `gorm:"column:id"`
		VersionNo int    `gorm:"column:version_no"`
		Name      string `gorm:"column:name"`
	}

	query := "(" + strings.Join(conditions, " OR ") + ")"
	if err := c.db.WithContext(ctx).
		Table(TableNameInternalDocumentLibrary).
		Select("id, version_no, name").
		Where(query, args...).
		Find(&docs).Error; err != nil {
		return nil, err
	}

	// 构建结果映射
	result := make(map[string]string)
	for _, doc := range docs {
		key := fmt.Sprintf("%s_%d", doc.ID, doc.VersionNo)
		result[key] = doc.Name
	}

	return result, nil
}

// GetByIDs 根据ID列表批量查询内部文档
// 功能: 根据文档ID列表批量查询内部文档信息
// 参数:
//   - ctx: 上下文
//   - ids: 文档ID列表
//
// 返回值:
//   - []InternalDocumentLibrary: 内部文档列表
//   - error: 错误信息
func (c *InternalDocumentLibraryClient) GetByIDs(ctx context.Context, ids []string) ([]InternalDocumentLibrary, error) {
	var docs []InternalDocumentLibrary
	if err := c.db.WithContext(ctx).Table(TableNameInternalDocumentLibrary).Where("id IN ?", ids).Find(&docs).Error; err != nil {
		return nil, err
	}
	return docs, nil
}

// GetDeprecatedVersionsByMainID 根据主文档ID查询所有作废版本
// 功能：查询指定主文档的所有作废版本（包括主文档和历史版本），只查询已通过审批的作废记录
// 参数：ctx - 上下文，mainID - 主文档ID
// 返回值：内部文档库列表，错误信息
func (c *InternalDocumentLibraryClient) GetDeprecatedVersionsByMainID(ctx context.Context, mainID string) ([]DeprecatedDocumentWithRecord, error) {
	var documents []DeprecatedDocumentWithRecord

	// 联查作废申请表，只查询已通过审批的作废记录
	query := `
		SELECT  idl.*,
			ddr.deprecation_record_id
		FROM internal_document_library idl
		INNER JOIN deprecation_document_relations ddr ON idl.id = ddr.document_id
		INNER JOIN deprecation_records dr ON ddr.deprecation_record_id = dr.id
		WHERE  idl.main_id = ?
		  AND idl.status = -1
		  AND dr.approval_status = 3
		ORDER BY idl.updated_at DESC
	`

	if err := c.db.WithContext(ctx).Raw(query, mainID).Scan(&documents).Error; err != nil {
		return nil, err
	}

	return documents, nil
}

// GetAllVersionsByMainID 根据主文档ID查询所有版本（包括主文档和历史版本）
// 功能：查询指定主文档的所有版本，不限制状态，用于计算首次发布日期
// 参数：ctx - 上下文，mainID - 主文档ID
// 返回值：内部文档库列表，错误信息
func (c *InternalDocumentLibraryClient) GetAllVersionsByMainID(ctx context.Context, mainID string) ([]InternalDocumentLibrary, error) {
	var documents []InternalDocumentLibrary

	// 查询所有版本（不限制状态）
	// 包括：
	// 1. 主文档ID等于指定ID的记录
	// 2. main_id等于指定ID的历史版本记录
	query := c.db.WithContext(ctx).Model(&InternalDocumentLibrary{}).
		Where("(id = ? OR main_id = ?)", mainID, mainID).
		Order("publish_date ASC, created_at ASC") // 按发布日期和创建时间升序排列，找最早的

	if err := query.Find(&documents).Error; err != nil {
		return nil, err
	}

	return documents, nil
}

// GetDocumentIDsByName 根据文档名称模糊查询文档ID列表
// 功能: 根据文档名称进行模糊查询，获取匹配的文档ID列表
// 参数:
//   - ctx: 上下文
//   - name: 文档名称（支持模糊匹配）
//
// 返回值:
//   - []string: 匹配的文档ID列表
//   - error: 错误信息
func (c *InternalDocumentLibraryClient) GetDocumentIDsByName(ctx context.Context, name string) ([]string, error) {
	if name == "" {
		return []string{}, nil
	}

	var documentIDs []string
	if err := c.db.WithContext(ctx).
		Table(TableNameInternalDocumentLibrary).
		Select("id").
		Where("name LIKE ?", "%"+name+"%").
		Find(&documentIDs).Error; err != nil {
		return nil, err
	}

	return documentIDs, nil
}

// GetDocumentIDsByNo 根据文档编号模糊查询文档ID列表
// 功能: 根据文档编号进行模糊查询，获取匹配的文档ID列表
// 参数:
//   - ctx: 上下文
//   - no: 文档编号（支持模糊匹配）
//
// 返回值:
//   - []string: 匹配的文档ID列表
//   - error: 错误信息
func (c *InternalDocumentLibraryClient) GetDocumentIDsByNo(ctx context.Context, no string) ([]string, error) {
	if no == "" {
		return []string{}, nil
	}

	var documentIDs []string
	if err := c.db.WithContext(ctx).
		Table(TableNameInternalDocumentLibrary).
		Select("id").
		Where("no LIKE ?", "%"+no+"%").
		Find(&documentIDs).Error; err != nil {
		return nil, err
	}

	return documentIDs, nil
}

// GetChangeRecords 查询内部文档变更记录
// 功能：根据文档ID查询该文档的所有历史版本记录，按更新时间倒序排列
// 参数：ctx - 上下文，documentID - 文档ID，page - 页码，pageSize - 每页大小，noPage - 是否不分页
// 返回值：变更记录列表，总数，错误信息
func (c *InternalDocumentLibraryClient) GetChangeRecords(ctx context.Context, documentID string, page, pageSize int, noPage bool) ([]InternalDocumentLibrary, int64, error) {
	var records []InternalDocumentLibrary
	var total int64

	// 构建查询条件：查询 main_id = documentID 的所有历史版本记录
	query := c.db.WithContext(ctx).Where("main_id = ?", documentID)

	// 获取总数
	if err := query.Model(&InternalDocumentLibrary{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 按更新时间倒序排列
	query = query.Order("updated_at DESC")

	// 根据 noPage 参数决定是否分页
	if !noPage {
		// 分页查询
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	if err := query.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// Create 创建内部文档记录
// 功能：创建一条新的内部文档记录
// 参数：ctx - 上下文，doc - 文档对象
// 返回值：错误信息
func (c *InternalDocumentLibraryClient) Create(ctx context.Context, doc *InternalDocumentLibrary) error {
	return c.db.WithContext(ctx).Create(doc).Error
}

// DeleteByID 根据ID删除内部文档记录
// 功能：根据文档ID删除内部文档记录
// 参数：ctx - 上下文，id - 文档ID
// 返回值：错误信息
func (c *InternalDocumentLibraryClient) DeleteByID(ctx context.Context, id string) error {
	return c.db.WithContext(ctx).Where("id = ?", id).Delete(&InternalDocumentLibrary{}).Error
}

// DeleteByDocumentNo 根据文档编号删除内部文档记录
// 功能：根据文档编号和组织ID删除内部文档记录
// 参数：ctx - 上下文，orgID - 组织ID，docNo - 文档编号
// 返回值：错误信息
func (c *InternalDocumentLibraryClient) DeleteByDocumentNo(ctx context.Context, orgID, docNo string) error {
	return c.db.WithContext(ctx).
		Where("organization_id = ? AND no = ?", orgID, docNo).
		Delete(&InternalDocumentLibrary{}).Error
}

// DeprecatedDocumentWithRecord 作废文档及其作废记录信息
type DeprecatedDocumentWithRecord struct {
	InternalDocumentLibrary        // 嵌入文档库结构体
	DeprecationRecordID     string `gorm:"column:deprecation_record_id"` // 作废记录ID
}

// GetDeprecationRecords 分页查询作废记录
// 功能：专门用于作废记录查询，只返回已作废的主文档记录
// 参数：ctx - 上下文，req - 分页查询请求
// 返回值：作废记录列表，总数，错误信息
// 异常：数据库查询失败
// 业务逻辑：
//   - 只查询状态为已作废的记录（status=3）
//   - 只查询主文档记录（main_id为空），不包括历史版本
//   - 这些记录都是作废申请中的文档

type InternalDeprecationRecord struct {
	DocumentID         string    `gorm:"column:document_id"`          // 主文档ID（改名以保持一致性）
	FirstPublishDate   time.Time `gorm:"column:first_publish_date"`   // 首次发布日期（最早的publish_date）
	FirstEffectiveDate time.Time `gorm:"column:first_effective_date"` // 首次实施日期（最早的effective_date）
	LastDeprecatedDate time.Time `gorm:"column:last_deprecated_date"` // 最后作废日期（最晚的updated_at）
	LatestDocumentNo   string    `gorm:"column:latest_document_no"`   // 最新记录的文档编号
	LatestDocumentName string    `gorm:"column:latest_document_name"` // 最新记录的文档名称
	LatestCategoryID   string    `gorm:"column:latest_category_id"`   // 最新记录的分类ID
	DeprecatedCount    int       `gorm:"column:deprecated_count"`     // 作废版本数量
}

func (c *InternalDocumentLibraryClient) PageInternalDeprecationRecordsSummary(ctx context.Context, req InternalDocumentLibraryPageReq) ([]InternalDeprecationRecord, int64, error) {
	// 构建基础查询条件：联查作废申请表，只查询已通过审批的记录
	whereConditions := []string{
		"idl.organization_id = ?",
		"idl.status = -1",        // 内部文档作废状态为-1
		"dr.approval_status = 3", // 只查询已审批通过的作废申请
	}
	args := []interface{}{req.OrganizationID}

	// 添加过滤条件
	if req.No != "" {
		whereConditions = append(whereConditions, "idl.no LIKE ?")
		args = append(args, "%"+req.No+"%")
	}
	if req.Name != "" {
		whereConditions = append(whereConditions, "idl.name LIKE ?")
		args = append(args, "%"+req.Name+"%")
	}
	if len(req.DocCategoryIds) > 0 {
		whereConditions = append(whereConditions, "idl.doc_category_id IN ?")
		args = append(args, req.DocCategoryIds)
	}
	if req.OriginalNo != "" {
		whereConditions = append(whereConditions, "idl.original_no LIKE ?")
		args = append(args, "%"+req.OriginalNo+"%")
	}
	// 附件过滤功能
	if req.HasAttachment == 1 {
		whereConditions = append(whereConditions, "idl.file_id IS NOT NULL AND idl.file_id != ''")
	} else if req.HasAttachment == 2 {
		whereConditions = append(whereConditions, "idl.file_id IS NULL OR idl.file_id = ''")
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// 先查询总数（按main_id分组后的个数）
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) as total
		FROM (
			SELECT idl.main_id
			FROM internal_document_library idl
			INNER JOIN deprecation_document_relations ddr ON idl.id = ddr.document_id
			INNER JOIN deprecation_records dr ON ddr.deprecation_record_id = dr.id
			WHERE %s
			GROUP BY idl.main_id
		) as grouped_results
	`, whereClause)

	var total int64
	if err := c.db.WithContext(ctx).Raw(countQuery, args...).Scan(&total).Error; err != nil {
		logc.Error(ctx, "Failed to count deprecated documents", err)
		return nil, 0, err
	}

	// 构建主查询，使用聚合函数获取分组最新记录信息
	selectQuery := fmt.Sprintf(`
		SELECT
			idl.main_id as document_id,
			MIN(idl.publish_date) as first_publish_date,
			MIN(idl.effective_date) as first_effective_date,
			MAX(idl.updated_at) as last_deprecated_date,
			SUBSTRING_INDEX(GROUP_CONCAT(idl.no ORDER BY idl.updated_at DESC), ',', 1) as latest_document_no,
			SUBSTRING_INDEX(GROUP_CONCAT(idl.name ORDER BY idl.updated_at DESC), ',', 1) as latest_document_name,
			SUBSTRING_INDEX(GROUP_CONCAT(idl.doc_category_id ORDER BY idl.updated_at DESC), ',', 1) as latest_category_id,
			SUBSTRING_INDEX(GROUP_CONCAT(idl.version ORDER BY idl.updated_at DESC), ',', 1) as latest_version,
			COUNT(*) as deprecated_count
		FROM internal_document_library idl
		INNER JOIN deprecation_document_relations ddr ON idl.id = ddr.document_id
		INNER JOIN deprecation_records dr ON ddr.deprecation_record_id = dr.id
		WHERE %s
		GROUP BY idl.main_id
		ORDER BY MAX(idl.updated_at) DESC
	`, whereClause)

	// 添加分页
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		selectQuery += fmt.Sprintf(" LIMIT %d OFFSET %d", req.PageSize, offset)
	}

	var results []InternalDeprecationRecord
	if err := c.db.WithContext(ctx).Raw(selectQuery, args...).Scan(&results).Error; err != nil {
		logc.Error(ctx, "Failed to query deprecated documents", err)
		return nil, 0, err
	}

	return results, total, nil
}
