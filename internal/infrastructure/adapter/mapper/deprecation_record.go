package mapper

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameDeprecationRecord = "deprecation_records"
)

// DeprecationRecord 对应 deprecation_records 表
type DeprecationRecord struct {
	ID             string         `gorm:"type:varchar(64);primary_key"`
	DeprecateAt    time.Time      `gorm:"comment:'拟定废弃日期'"`
	ApprovalStatus int32          `gorm:"comment:'审批状态: -1=系统处理中, 1=待提交, 2=待审批, 3=已审批, 4=已驳回'"`
	Reason         int32          `gorm:"comment:'作废原因类型'"`
	OtherReason    string         `gorm:"type:varchar(512);comment:'其他作废原因'"`
	WorkflowID     string         `gorm:"type:varchar(64);comment:'工作流ID'"`
	ApprovalInfo   datatypes.JSON `gorm:"type:json;comment:'审批信息'"`
	OrganizationID string         `gorm:"type:varchar(64);index;comment:'组织ID'"`
	TenantID       string         `gorm:"type:varchar(64);index;comment:'租户ID'"`
	CreatedAt      time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt      time.Time      `gorm:"column:updated_at"` // 更新时间
	CreatedBy      string         `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy      string         `gorm:"type:varchar(64);column:updated_by"`
}

func (DeprecationRecord) TableName() string {
	return TableNameDeprecationRecord
}

// DeprecationRecordClient 是 deprecation_records 表的数据访问客户端
type DeprecationRecordClient struct {
	db *gorm.DB
}

// NewDeprecationRecordClient 创建一个新的 DeprecationRecordClient 实例
// 功能: 创建并初始化作废记录数据访问客户端
// 参数:
//
//	db: DocvaultDB数据库连接实例
//
// 返回值:
//
//	*DeprecationRecordClient: 作废记录客户端实例
//
// 异常: 无
func NewDeprecationRecordClient(db *DocvaultDB) *DeprecationRecordClient {
	return &DeprecationRecordClient{
		db: db.GetDB(),
	}
}

// GetByID 根据 ID 获取 DeprecationRecord 记录
// 功能: 根据主键ID查询作废记录
// 参数:
//
//	ctx: 上下文对象
//	id: 作废记录ID
//
// 返回值:
//
//	*DeprecationRecord: 作废记录对象，未找到时返回nil
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *DeprecationRecordClient) GetByID(ctx context.Context, id string) (*DeprecationRecord, error) {
	var record DeprecationRecord
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logc.Error(ctx, "Failed to get deprecation record by ID", err)
		return nil, err
	}
	return &record, nil
}

// Update 更新 DeprecationRecord 记录
// 功能: 更新作废记录信息
// 参数:
//
//	ctx: 上下文对象
//	record: 作废记录对象
//
// 返回值:
//
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *DeprecationRecordClient) Update(ctx context.Context, record *DeprecationRecord) error {
	if err := c.db.WithContext(ctx).Save(record).Error; err != nil {
		logc.Error(ctx, "Failed to update deprecation record", err)
		return err
	}
	return nil
}

// DeprecationRecordPageReq 作废记录分页查询请求
type DeprecationRecordPageReq struct {
	Page                int      `json:"page"`               // 页码
	PageSize            int      `json:"pageSize"`           // 每页大小
	NoPage              bool     `json:"noPage"`             // 是否不分页
	OrganizationID      string   `json:"organizationId"`     // 组织ID（必需）
	TenantID            string   `json:"tenantId"`           // 租户ID（必需）
	DocumentNo          string   `json:"documentNo"`         // 文档编号
	DocumentName        string   `json:"documentName"`       // 文档名称
	DocumentModuleType  int32    `json:"documentModuleType"` // 文档模块：2内部文档，3外部文档
	DocumentCategoryIDs []string `json:"documentCategoryId"` // 文档类别ID列表
	ApprovalStatus      int32    `json:"approvalStatus"`     // 审批状态
	Applicant           string   `json:"applicant"`          // 申请人昵称（模糊匹配）
	CreatedBy           string   `json:"createdBy"`          // 创建人（单个用户ID）
	CreatedByIDs        []string `json:"createdByIds"`       // 创建人列表（多个用户ID，用于申请人昵称匹配）
}

// DeprecationDocumentInfo 作废文档信息
type DeprecationDocumentInfo struct {
	DocumentID         string `json:"documentId"`         // 文档ID
	DocumentName       string `json:"documentName"`       // 文档名称
	DocumentNo         string `json:"documentNo"`         // 文档编号
	DocumentVersionNo  string `json:"documentVersionNo"`  // 文档版本号
	DocumentModule     int    `json:"documentModule"`     // 文档模块类型
	DocumentStatus     int8   `json:"documentStatus"`     // 文档状态
	DocumentCategoryID string `json:"documentCategoryId"` // 文档类别ID
}

// GetDeprecationRecords 分页查询作废记录
// 功能: 查询作废记录，支持文档相关筛选条件
// 参数:
//
//	ctx: 上下文对象
//	req: 分页查询请求参数
//
// 返回值:
//
//	[]DeprecationRecord: 作废记录列表
//	int64: 总记录数
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *DeprecationRecordClient) GetDeprecationRecords(ctx context.Context, req DeprecationRecordPageReq) ([]DeprecationRecord, int64, error) {
	var records []DeprecationRecord
	var total int64

	// 构建基础查询条件
	query := c.db.WithContext(ctx).Model(&DeprecationRecord{}).
		Where("deprecation_records.organization_id = ? AND deprecation_records.tenant_id = ?", req.OrganizationID, req.TenantID)

	// 添加筛选条件
	if req.ApprovalStatus != 0 {
		query = query.Where("deprecation_records.approval_status = ?", req.ApprovalStatus)
	}

	if req.CreatedBy != "" {
		query = query.Where("deprecation_records.created_by = ?", req.CreatedBy)
	}

	// 如果有申请人昵称查询结果（多个用户ID），使用IN查询
	if len(req.CreatedByIDs) > 0 {
		query = query.Where("deprecation_records.created_by IN ?", req.CreatedByIDs)
	}

	// 如果有文档相关的筛选条件，需要关联查询
	if req.DocumentName != "" || req.DocumentNo != "" || req.DocumentModuleType != 0 || len(req.DocumentCategoryIDs) > 0 {
		query = c.addDocumentFiltersForList(query, req)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logc.Error(ctx, "Failed to count deprecation records", err)
		return nil, 0, err
	}

	// 按创建时间倒序排列
	query = query.Order("deprecation_records.created_at DESC")

	// 分页处理
	if !req.NoPage {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	// 查询记录
	if err := query.Find(&records).Error; err != nil {
		logc.Error(ctx, "Failed to get deprecation records", err)
		return nil, 0, err
	}

	return records, total, nil
}

// addDocumentFiltersForList 添加文档相关的筛选条件（用于列表查询）
func (c *DeprecationRecordClient) addDocumentFiltersForList(query *gorm.DB, req DeprecationRecordPageReq) *gorm.DB {
	// 关联查询文档关系表和文档视图，添加字符集转换解决字符集冲突
	query = query.Joins("JOIN deprecation_document_relations ddr ON deprecation_records.id = ddr.deprecation_record_id").
		Joins("JOIN all_documents_view adv ON CONVERT(ddr.document_id USING utf8mb4) COLLATE utf8mb4_unicode_ci = CONVERT(adv.document_id USING utf8mb4) COLLATE utf8mb4_unicode_ci")

	// 添加文档筛选条件
	if req.DocumentName != "" {
		query = query.Where("adv.document_name LIKE ?", "%"+req.DocumentName+"%")
	}

	if req.DocumentNo != "" {
		query = query.Where("adv.document_no LIKE ?", "%"+req.DocumentNo+"%")
	}

	if req.DocumentModuleType != 0 {
		query = query.Where("adv.document_module = ?", req.DocumentModuleType)
	}

	if len(req.DocumentCategoryIDs) > 0 {
		query = query.Where("adv.document_category_id IN ?", req.DocumentCategoryIDs)
	}

	// 去重，因为一个作废记录可能关联多个文档
	// 在COUNT查询和数据查询时使用不同的去重方式
	query = query.Distinct("deprecation_records.id")

	return query
}

// Create 创建作废记录
// 功能: 创建一条新的作废记录
// 参数:
//   - ctx: 上下文对象
//   - record: 作废记录对象
//
// 返回值:
//   - error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *DeprecationRecordClient) Create(ctx context.Context, record *DeprecationRecord) error {
	if err := c.db.WithContext(ctx).Create(record).Error; err != nil {
		logc.Error(ctx, "Failed to create deprecation record", err)
		return err
	}
	return nil
}

// DeleteByID 根据ID删除作废记录
// 功能: 根据主键ID删除作废记录
// 参数:
//   - ctx: 上下文对象
//   - id: 作废记录ID
//
// 返回值:
//   - error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *DeprecationRecordClient) DeleteByID(ctx context.Context, id string) error {
	if err := c.db.WithContext(ctx).Where("id = ?", id).Delete(&DeprecationRecord{}).Error; err != nil {
		logc.Error(ctx, "Failed to delete deprecation record by ID", err)
		return err
	}
	return nil
}

// GetDocumentsByDeprecationRecordID 根据作废记录ID查询关联的文档信息
// 功能: 通过作废记录ID，关联查询文档关系表和统一文档视图，获取文档详细信息
// 参数:
//
//	ctx: 上下文对象
//	deprecationRecordID: 作废记录ID
//
// 返回值:
//
//	[]DeprecationDocumentInfo: 关联文档信息列表
//	error: 错误信息，成功时为nil
//
// 异常: 数据库操作异常
func (c *DeprecationRecordClient) GetDocumentsByDeprecationRecordID(ctx context.Context, deprecationRecordID string) ([]DeprecationDocumentInfo, error) {
	var documents []DeprecationDocumentInfo

	// 关联查询：deprecation_document_relations -> all_documents_view
	// 添加字符集转换解决字符集冲突问题
	query := c.db.WithContext(ctx).
		Table("deprecation_document_relations ddr").
		Select(`
			adv.document_id,
			adv.document_name,
			adv.document_no,
			adv.document_version_no,
			adv.document_module,
			adv.document_status,
			adv.document_category_id
		`).
		Joins("JOIN all_documents_view adv ON CONVERT(ddr.document_id USING utf8mb4) COLLATE utf8mb4_unicode_ci = CONVERT(adv.document_id USING utf8mb4) COLLATE utf8mb4_unicode_ci").
		Where("ddr.deprecation_record_id = ?", deprecationRecordID).
		Order("ddr.id ASC") // 保持顺序

	// 执行查询
	if err := query.Find(&documents).Error; err != nil {
		logc.Error(ctx, "Failed to get documents by deprecation record ID", err)
		return nil, err
	}

	return documents, nil
}
