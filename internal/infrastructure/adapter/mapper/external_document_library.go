package mapper

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameExternalDocumentLibrary = "external_document_library"
)

type ExternalDocumentLibraryClient struct {
	db *gorm.DB
}

func NewExternalDocumentLibraryClient(db *DocvaultDB) *ExternalDocumentLibraryClient {
	return &ExternalDocumentLibraryClient{db: db.GetDB()}
}

func (c *ExternalDocumentLibraryClient) CreateInBatches(ctx context.Context, externalDocuments []ExternalDocumentLibrary) error {
	return c.db.WithContext(ctx).CreateInBatches(externalDocuments, 100).Error
}

func (c *ExternalDocumentLibraryClient) GetByID(ctx context.Context, id string) (ExternalDocumentLibrary, error) {
	externalDocument := ExternalDocumentLibrary{}
	if err := c.db.WithContext(ctx).Where("id = ?", id).First(&externalDocument).Error; err != nil {
		return ExternalDocumentLibrary{}, err
	}
	return externalDocument, nil
}

func (c *ExternalDocumentLibraryClient) Update(ctx context.Context, externalDocument ExternalDocumentLibrary) error {
	return c.db.WithContext(ctx).Model(&externalDocument).Where("id=?", externalDocument.ID).Updates(externalDocument).Error
}

func (c *ExternalDocumentLibraryClient) BatchGetMaxNumberPostfix(ctx context.Context, orgID string, TypeDictionaryNodeIds []string) ([]DictNodeIdAndNumberPostfix, error) {
	var result []DictNodeIdAndNumberPostfix
	err := c.db.WithContext(ctx).Model(&ExternalDocumentLibrary{}).Select("type_dictionary_node_id as dict_node_id, max(number_postfix) as number_postfix").Where("organization_id = ? AND type_dictionary_node_id IN ?", orgID, TypeDictionaryNodeIds).Group("type_dictionary_node_id").Find(&result).Error
	return result, err
}

func (c *ExternalDocumentLibraryClient) GetMaxNumberPostfix(ctx context.Context, orgID string, TypeDictionaryNodeIds string) (int, error) {
	maxNo := 0
	if err := c.db.WithContext(ctx).Table(TableNameExternalDocumentLibrary).Select("ifnull(max(number_postfix), 0) as max_no").Where("organization_id = ? and type_dictionary_node_id = ?", orgID, TypeDictionaryNodeIds).Find(&maxNo).Error; err != nil {
		return 0, err
	}
	return maxNo, nil
}

// ExternalDeprecationRecord 外部文档作废记录聚合结构
type ExternalDeprecationRecord struct {
	DocumentID           string    `gorm:"column:document_id"`            // 主文档ID
	FirstPublishDate     time.Time `gorm:"column:first_publish_date"`     // 首次发布日期（最早的publish_date）
	FirstEffectiveDate   time.Time `gorm:"column:first_effective_date"`   // 首次实施日期（最早的effective_date）
	LastDeprecatedDate   time.Time `gorm:"column:last_deprecated_date"`   // 最后作废日期（最晚的updated_at）
	LatestDocumentNo     string    `gorm:"column:latest_document_no"`     // 最新记录的文档编号
	LatestDocumentName   string    `gorm:"column:latest_document_name"`   // 最新记录的文档名称
	LatestTypeID         string    `gorm:"column:latest_type_id"`         // 最新记录的类型ID
	LatestOriginalNumber string    `gorm:"column:latest_original_number"` // 最新记录的原文件号
	DeprecatedCount      int       `gorm:"column:deprecated_count"`       // 作废版本数量
}

// PageExternalDeprecationRecordsSummary 分页查询外部库作废记录汇总
// 功能：按main_id分组查询外部作废文档汇总信息，只查询已通过审批的作废申请
// 参数：ctx - 上下文，req - 分页查询请求
// 返回值：外部作废记录汇总列表，总数，错误信息
// 异常：数据库查询失败
// 业务逻辑：
//   - 联查外部文档库、作废文档关系表和作废申请表
//   - 只查询已审批通过的作废申请（approval_status=3）
//   - 按main_id分组，聚合获取最新文档信息和作废版本数量
//   - 支持按文档编号、名称、类型等条件过滤
func (c *ExternalDocumentLibraryClient) PageExternalDeprecationRecordsSummary(ctx context.Context, req ExternalDocumentPage) ([]ExternalDeprecationRecord, int64, error) {
	// 构建基础查询条件：联查作废申请表，只查询已通过审批的记录
	whereConditions := []string{
		"edl.organization_id = ?",
		"edl.status = -1",        // 外部文档作废状态为-1
		"dr.approval_status = 3", // 只查询已审批通过的作废申请
	}
	args := []interface{}{req.OrgID}

	// 添加过滤条件
	if req.Number != "" {
		whereConditions = append(whereConditions, "edl.number LIKE ?")
		args = append(args, "%"+req.Number+"%")
	}
	if req.Name != "" {
		whereConditions = append(whereConditions, "edl.name LIKE ?")
		args = append(args, "%"+req.Name+"%")
	}
	if req.OriginalNumber != "" {
		whereConditions = append(whereConditions, "edl.original_number LIKE ?")
		args = append(args, "%"+req.OriginalNumber+"%")
	}
	if req.OriginalDocNumber != "" {
		whereConditions = append(whereConditions, "edl.original_doc_number LIKE ?")
		args = append(args, "%"+req.OriginalDocNumber+"%")
	}
	if req.PublishDocNumber != "" {
		whereConditions = append(whereConditions, "edl.publish_doc_number LIKE ?")
		args = append(args, "%"+req.PublishDocNumber+"%")
	}
	if req.PublishDepartment != "" {
		whereConditions = append(whereConditions, "edl.publish_department LIKE ?")
		args = append(args, "%"+req.PublishDepartment+"%")
	}
	if len(req.TypeDictionaryNodeIds) > 0 {
		whereConditions = append(whereConditions, "edl.type_dictionary_node_id IN ?")
		args = append(args, req.TypeDictionaryNodeIds)
	}
	if req.DomainDictionaryNodeId != "" {
		whereConditions = append(whereConditions, "edl.domain_dictionary_node_id = ?")
		args = append(args, req.DomainDictionaryNodeId)
	}
	if req.AuthenticationDictionaryNodeId != "" {
		whereConditions = append(whereConditions, "edl.authentication_dictionary_node_ids LIKE ?")
		args = append(args, "%"+req.AuthenticationDictionaryNodeId+"%")
	}
	// 附件过滤功能
	if req.BeAttachedFile == "1" {
		whereConditions = append(whereConditions, "edl.file_id IS NOT NULL AND edl.file_id != ''")
	} else if req.BeAttachedFile == "2" {
		whereConditions = append(whereConditions, "edl.file_id IS NULL OR edl.file_id = ''")
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// 先查询总数（按main_id分组后的个数）
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) as total
		FROM (
			SELECT CASE WHEN edl.main_id IS NOT NULL AND edl.main_id != '' THEN edl.main_id ELSE edl.id END as main_doc_id
			FROM external_document_library edl
			INNER JOIN deprecation_document_relations ddr ON edl.id = ddr.document_id
			INNER JOIN deprecation_records dr ON ddr.deprecation_record_id = dr.id
			WHERE %s
			GROUP BY CASE WHEN edl.main_id IS NOT NULL AND edl.main_id != '' THEN edl.main_id ELSE edl.id END
		) as grouped_results
	`, whereClause)

	var total int64
	if err := c.db.WithContext(ctx).Raw(countQuery, args...).Scan(&total).Error; err != nil {
		logc.Error(ctx, "Failed to count external deprecated documents", err)
		return nil, 0, err
	}

	// 构建主查询，使用聚合函数获取分组最新记录信息
	selectQuery := fmt.Sprintf(`
		SELECT
			CASE WHEN edl.main_id IS NOT NULL AND edl.main_id != '' THEN edl.main_id ELSE edl.id END as document_id,
			MIN(edl.publish_date) as first_publish_date,
			MIN(edl.effective_date) as first_effective_date,
			MAX(edl.updated_at) as last_deprecated_date,
			SUBSTRING_INDEX(GROUP_CONCAT(edl.number ORDER BY edl.updated_at DESC), ',', 1) as latest_document_no,
			SUBSTRING_INDEX(GROUP_CONCAT(edl.name ORDER BY edl.updated_at DESC), ',', 1) as latest_document_name,
			SUBSTRING_INDEX(GROUP_CONCAT(edl.type_dictionary_node_id ORDER BY edl.updated_at DESC), ',', 1) as latest_type_id,
			SUBSTRING_INDEX(GROUP_CONCAT(edl.original_number ORDER BY edl.updated_at DESC), ',', 1) as latest_original_number,
			COUNT(*) as deprecated_count
		FROM external_document_library edl
		INNER JOIN deprecation_document_relations ddr ON edl.id = ddr.document_id
		INNER JOIN deprecation_records dr ON ddr.deprecation_record_id = dr.id
		WHERE %s
		GROUP BY CASE WHEN edl.main_id IS NOT NULL AND edl.main_id != '' THEN edl.main_id ELSE edl.id END
		ORDER BY MAX(edl.updated_at) DESC
	`, whereClause)

	// 添加分页
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		selectQuery += fmt.Sprintf(" LIMIT %d OFFSET %d", req.PageSize, offset)
	}

	var results []ExternalDeprecationRecord
	if err := c.db.WithContext(ctx).Raw(selectQuery, args...).Scan(&results).Error; err != nil {
		logc.Error(ctx, "Failed to query external deprecated documents", err)
		return nil, 0, err
	}

	return results, total, nil
}

// GetExternalDocumentByNumberAndName 根据 number 和 name 和 组织ID 查询外部文档，number 和 name 可以为空
func (c *ExternalDocumentLibraryClient) GetExternalDocumentByNumberAndName(ctx context.Context, number, name, orgID string) ([]ExternalDocumentLibrary, error) {
	var docs []ExternalDocumentLibrary
	query := c.db.WithContext(ctx).WithContext(ctx)
	if number != "" {
		query = query.Where("number LIKE ?", "%"+number+"%")
	}
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	err := query.Where("organization_id = ? AND status IN (1, 2, 3, 4)", orgID).Find(&docs).Error
	return docs, err
}

// ExternalDocumentLibrary 是 external_document_library 表的GORM模型
type ExternalDocumentLibrary struct {
	ID                              string         `gorm:"type:varchar(64);primary_key"`                                // id
	MainID                          string         `gorm:"type:varchar(64);column:main_id"`                             // 自关联ID 仅在有历史版本时有值
	GroupDocID                      string         `gorm:"type:varchar(64);column:group_doc_id"`                        // 集团文档 id  仅分公司纳入时有值
	Number                          string         `gorm:"type:varchar(255);column:number"`                             // 编号
	Version                         string         `gorm:"type:varchar(255);column:version"`                            // 版本
	NumberPostfix                   int            `gorm:"type:int(11);column:number_postfix"`                          // 编号后缀
	OriginalNumber                  string         `gorm:"type:varchar(255);column:original_number"`                    // 原文件编号
	OriginalVersion                 string         `gorm:"type:varchar(255);column:original_version"`                   // 原文件版本
	Name                            string         `gorm:"type:varchar(255);column:name"`                               // 文件名称
	EnglishName                     string         `gorm:"type:varchar(255);column:english_name"`                       // 英文名称
	DocType                         string         `gorm:"type:varchar(255);column:doc_type"`                           // 类别
	Domain                          string         `gorm:"type:varchar(255);column:domain"`                             // 领域
	OriginalDocNumber               string         `gorm:"type:varchar(255);column:original_doc_number"`                // 原文件号
	PublishDocNumber                string         `gorm:"type:varchar(255);column:publish_doc_number"`                 // 发文号
	PublishDepartment               string         `gorm:"type:varchar(255);column:publish_department"`                 // 发文部门
	PublishDate                     time.Time      `gorm:"type:datetime;column:publish_date"`                           // 发布日期
	EffectiveDate                   time.Time      `gorm:"type:datetime;column:effective_date"`                         // 实施日期
	Authentications                 string         `gorm:"type:varchar(500);column:authentications"`                    // 认证方式
	Status                          int8           `gorm:"type:tinyint;column:status"`                                  // 状态 状态（1即将作废 | 2即将实施 | 3有效 | 4 拟修订|-1 作废 | -2纳入审批中）
	TypeDictionaryNodeId            string         `gorm:"type:varchar(64);column:type_dictionary_node_id"`             // 类别ID
	DomainDictionaryNodeId          string         `gorm:"type:varchar(64);column:domain_dictionary_node_id"`           // 领域类型id
	AuthenticationDictionaryNodeIds string         `gorm:"type:varchar(500);column:authentication_dictionary_node_ids"` // 认证类型id
	FileID                          string         `gorm:"type:varchar(64);column:file_id"`                             // 文件id
	OrganizationID                  string         `gorm:"type:varchar(64);column:organization_id"`                     // 组织id
	ApprovalInfo                    datatypes.JSON `gorm:"type:json;column:approval_info;comment:'审批信息'"`               // 审批信息，存储为JSON格式
	OperationType                   int8           `gorm:"type:tinyint;column:operation_type"`                          // 操作类型 0-无操作 1- 新增,2- 修订,3-作废 仅在有历史版本时有值
	CreatedBy                       string         `gorm:"type:varchar(64);column:created_by"`                          // 创建人
	UpdatedBy                       string         `gorm:"type:varchar(64);column:updated_by"`                          // 更新人
	CreatedAt                       time.Time      `gorm:"column:created_at"`                                           // 创建时间
	UpdatedAt                       time.Time      `gorm:"column:updated_at"`                                           // 更新时间
	TenantID                        string         `gorm:"type:varchar(64);column:tenant_id"`                           // 租户ID
	ReplacementDocName              string         `gorm:"type:varchar(255);column:replacement_doc_name"`               // 替代文件名称
	ReplacementDocVersion           string         `gorm:"type:varchar(255);column:replacement_doc_version"`            // 替代版本版次
	Remark                          string         `gorm:"type:varchar(500);column:remark"`                             // 备注
}

// TableName 指定GORM模型对应的表名
func (ExternalDocumentLibrary) TableName() string {
	return TableNameExternalDocumentLibrary
}

type DictNodeIdAndNumberPostfix struct {
	DictNodeId    string
	NumberPostfix int
}

type ExternalDocumentPage struct {
	Page                           int
	PageSize                       int
	NoPage                         bool
	Number                         string
	Name                           string
	OriginalNumber                 string
	OriginalDocNumber              string
	PublishDocNumber               string
	PublishDepartment              string
	TypeDictionaryNodeIds          []string
	DomainDictionaryNodeId         string
	AuthenticationDictionaryNodeId string
	BeAttachedFile                 string
	Status                         int
	OrgID                          string
	TenantID                       string
}

// BatchUpdateDocumentStatus 批量根据 id 列表更新 status
func (c *ExternalDocumentLibraryClient) BatchUpdateDocumentStatus(ctx context.Context, ids []string, newStatus int8) error {
	if len(ids) == 0 {
		return nil
	}
	return c.db.WithContext(ctx).
		Table(TableNameExternalDocumentLibrary).
		Where("id IN ?", ids).
		Update("status", newStatus).Error
}

func (c *ExternalDocumentLibraryClient) GetByIDs(ctx context.Context, ids []string) ([]ExternalDocumentLibrary, error) {
	var docs []ExternalDocumentLibrary
	if err := c.db.WithContext(ctx).Table(TableNameExternalDocumentLibrary).Where("id IN ?", ids).Find(&docs).Error; err != nil {
		return nil, err
	}
	return docs, nil
}

// GetDocumentIDsByName 根据文档名称模糊查询文档ID列表
// 功能: 根据文档名称进行模糊查询，获取匹配的文档ID列表
// 参数:
//   - ctx: 上下文
//   - name: 文档名称（支持模糊匹配）
//   - organizationID: 组织ID
//
// 返回值:
//   - []string: 匹配的文档ID列表
//   - error: 错误信息
func (c *ExternalDocumentLibraryClient) GetDocumentIDsByName(ctx context.Context, name string) ([]string, error) {
	if name == "" {
		return []string{}, nil
	}

	var documentIDs []string
	if err := c.db.WithContext(ctx).
		Table(TableNameExternalDocumentLibrary).
		Select("id").
		Where("name LIKE ?", "%"+name+"%").
		Find(&documentIDs).Error; err != nil {
		return nil, err
	}

	return documentIDs, nil
}

// GetDocumentIDsByNo 根据文档编号模糊查询文档ID列表
// 功能: 根据文档编号进行模糊查询，获取匹配的文档ID列表
// 参数:
//   - ctx: 上下文
//   - no: 文档编号（支持模糊匹配）
//   - organizationID: 组织ID
//
// 返回值:
//   - []string: 匹配的文档ID列表
//   - error: 错误信息
func (c *ExternalDocumentLibraryClient) GetDocumentIDsByNo(ctx context.Context, no string) ([]string, error) {
	if no == "" {
		return []string{}, nil
	}

	var documentIDs []string
	if err := c.db.WithContext(ctx).
		Table(TableNameExternalDocumentLibrary).
		Select("id").
		Where("number LIKE ?", "%"+no+"%").
		Find(&documentIDs).Error; err != nil {
		return nil, err
	}

	return documentIDs, nil
}

// GetApprovalInfo 获取审批信息
// 功能：解析JSON格式的审批信息
// 返回值：审批信息结构体
func (e ExternalDocumentLibrary) GetApprovalInfo() ApprovalInfo {
	approvalInfo, err := e.ApprovalInfo.MarshalJSON()
	if err != nil {
		return ApprovalInfo{}
	}
	approvalInfoValue := ApprovalInfo{}
	if err := json.Unmarshal(approvalInfo, &approvalInfoValue); err != nil {
		return ApprovalInfo{}
	}
	return approvalInfoValue
}

// GetAuthenticationsSlice 获取认证方式切片
// 功能：将逗号分隔的认证方式字符串转换为切片
// 返回值：认证方式切片
func (e ExternalDocumentLibrary) GetAuthenticationsSlice() []string {
	if e.Authentications == "" {
		return []string{}
	}
	return strings.Split(e.Authentications, ",")
}

// GetChangeRecords 查询文档变更记录
// 功能：根据文档ID查询该文档的所有历史版本记录，按更新时间倒序排列
// 参数：ctx - 上下文，documentID - 文档ID，page - 页码，pageSize - 每页大小，noPage - 是否不分页
// 返回值：变更记录列表，总数，错误信息
func (c *ExternalDocumentLibraryClient) GetChangeRecords(ctx context.Context, documentID string, page, pageSize int, noPage bool) ([]ExternalDocumentLibrary, int64, error) {
	var records []ExternalDocumentLibrary
	var total int64

	// 构建查询条件：查询 main_id = documentID 的所有历史版本记录
	query := c.db.WithContext(ctx).Where("main_id = ?", documentID)

	// 获取总数
	if err := query.Model(&ExternalDocumentLibrary{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 按更新时间倒序排列
	query = query.Order("updated_at DESC")

	// 根据 noPage 参数决定是否分页
	if !noPage {
		// 分页查询
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	if err := query.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetDeprecatedVersionsByMainID 根据主文档ID查询所有作废版本
// 功能：查询指定主文档的所有作废版本（包括主文档和历史版本），只查询已通过审批的作废记录
// 参数：ctx - 上下文，mainID - 主文档ID
// 返回值：外部文档库列表，错误信息
func (c *ExternalDocumentLibraryClient) GetDeprecatedVersionsByMainID(ctx context.Context, mainID string) ([]ExternalDocumentWithRecord, error) {
	var documents []ExternalDocumentWithRecord

	// 联查作废申请表，只查询已通过审批的作废记录
	query := `
		SELECT  edl.*,
			ddr.deprecation_record_id
		FROM external_document_library edl
		INNER JOIN deprecation_document_relations ddr ON edl.id = ddr.document_id
		INNER JOIN deprecation_records dr ON ddr.deprecation_record_id = dr.id
		WHERE  edl.main_id = ?
		  AND edl.status = -1
		  AND dr.approval_status = 3
		ORDER BY edl.updated_at DESC
	`

	if err := c.db.WithContext(ctx).Raw(query, mainID, mainID).Scan(&documents).Error; err != nil {
		logc.Error(ctx, "查询外部作废文档版本失败", err)
		return nil, err
	}

	return documents, nil
}

type ExternalDocumentWithRecord struct {
	ExternalDocumentLibrary        // 嵌入外部文档库结构体
	DeprecationRecordID     string `gorm:"column:deprecation_record_id"` // 作废记录ID
}

// GetVersionsByMainID 根据主文档ID查询所有版本（包括有效和作废状态）
// 功能：查询指定主文档的所有版本
// 参数：ctx - 上下文，mainID - 主文档ID
// 返回值：外部文档库列表，错误信息
func (c *ExternalDocumentLibraryClient) GetVersionsByMainID(ctx context.Context, mainID string) ([]ExternalDocumentLibrary, error) {
	var documents []ExternalDocumentLibrary

	// 查询所有版本（不限制状态）
	query := c.db.WithContext(ctx).Model(&ExternalDocumentLibrary{}).
		Where("(id = ? OR main_id = ?)", mainID, mainID).
		Order("created_at DESC") // 按创建时间倒序排列

	if err := query.Find(&documents).Error; err != nil {
		logc.Error(ctx, "查询外部文档版本失败", err)
		return nil, err
	}

	return documents, nil
}

// GetDeprecatedDocumentIDsByMainIDAndID 根据主文档ID和文档ID列表查询已作废的文档ID
// 功能：直接从external_document_library表查询已作废状态的文档ID
// 参数：ctx - 上下文，documentIDs - 要检查的文档ID列表
// 返回值：已作废的文档ID列表，错误信息
// 业务逻辑：查询 status = -1（已作废）且文档ID或主文档ID在传入列表中的文档
func (c *ExternalDocumentLibraryClient) GetDeprecatedDocumentIDsByMainIDAndID(ctx context.Context, documentIDs []string) ([]string, error) {
	if len(documentIDs) == 0 {
		return []string{}, nil
	}

	var deprecatedDocumentIDs []string

	// 直接查询external_document_library表，查询已作废状态的文档
	// 查询条件：
	// 1. status = -1 (已作废状态)
	// 2. id 在传入的文档ID列表中，或者 main_id 在传入的文档ID列表中
	query := c.db.WithContext(ctx).
		Table(TableNameExternalDocumentLibrary).
		Select("id").
		Where("status = ?", -1).
		Where("id IN ? OR main_id IN ?", documentIDs, documentIDs)

	if err := query.Find(&deprecatedDocumentIDs).Error; err != nil {
		logc.Error(ctx, "查询外部已作废文档ID失败", err)
		return nil, err
	}

	return deprecatedDocumentIDs, nil
}

// ExternalDocumentDateAggregation 外部文档日期聚合结构（与内部文档的DocumentDateAggregation对应）
type ExternalDocumentDateAggregation struct {
	DocumentID         string    `gorm:"column:document_id"`          // 文档ID
	FirstPublishDate   time.Time `gorm:"column:first_publish_date"`   // 首次发布日期（最早的publish_date）
	FirstEffectiveDate time.Time `gorm:"column:first_effective_date"` // 首次实施日期（最早的effective_date）
	LastDeprecatedDate time.Time `gorm:"column:last_deprecated_date"` // 最后作废日期（最晚的updated_at）
}

// GetExternalDocumentDateAggregationsByDocumentIDs 根据文档ID列表批量查询外部文档日期聚合信息
// 功能：使用SQL聚合函数一次性查询多个文档的首次发布日期、首次实施日期和最后作废日期
// 参数：ctx - 上下文，documentIDs - 文档ID列表（来自已作废的文档）
// 返回值：文档日期聚合结果映射，错误信息
// 业务逻辑：
//   - FirstPublishDate：查询该文档所有版本中最早的 publish_date
//   - FirstEffectiveDate：查询该文档所有版本中最早的 effective_date
//   - LastDeprecatedDate：查询该文档所有版本中最晚的 updated_at
//
// 查询流程：
//  1. 直接使用传入的文档ID列表在external_document_library表中查询并聚合日期
//  2. 按主文档分组（使用main_id或id）进行聚合
func (c *ExternalDocumentLibraryClient) GetExternalDocumentDateAggregationsByDocumentIDs(ctx context.Context, documentIDs []string) (map[string]ExternalDocumentDateAggregation, error) {
	if len(documentIDs) == 0 {
		return make(map[string]ExternalDocumentDateAggregation), nil
	}

	var results []ExternalDocumentDateAggregation

	// 构建SQL查询，使用聚合函数同时获取所有日期信息
	// 直接使用文档ID列表查询，按主文档分组聚合
	// 注意：外部文档使用main_id字段，与内部文档的main_id字段含义相同
	query := `
		SELECT
			CASE
				WHEN main_id IS NOT NULL AND main_id != '' THEN main_id
				ELSE id
			END as document_id,
			MIN(publish_date) as first_publish_date,
			MIN(effective_date) as first_effective_date,
			MAX(updated_at) as last_deprecated_date
		FROM external_document_library
		WHERE id IN (?)
		GROUP BY CASE
			WHEN main_id IS NOT NULL AND main_id != '' THEN main_id
			ELSE id
		END
	`

	if err := c.db.WithContext(ctx).Raw(query, documentIDs).Scan(&results).Error; err != nil {
		logc.Error(ctx, "查询外部文档日期聚合信息失败", err)
		return nil, err
	}

	// 转换为映射格式
	resultMap := make(map[string]ExternalDocumentDateAggregation)
	for _, result := range results {
		resultMap[result.DocumentID] = result
	}

	return resultMap, nil
}

// ExternalDeprecatedDocumentWithRecord 外部作废文档及作废记录信息结构
type ExternalDeprecatedDocumentWithRecord struct {
	ExternalDocumentLibrary        // 嵌入外部文档库结构
	DeprecationRecordID     string `gorm:"column:deprecation_record_id"` // 作废记录ID
}

// GetExternalDeprecatedDocumentVersionsWithRecords 使用内联查询获取外部作废文档版本及其作废记录信息
// 功能：通过JOIN查询同时获取外部文档库信息和对应的作废记录ID，只查询已通过审批的作废记录
// 参数：ctx - 上下文，mainID - 主文档ID
// 返回值：外部作废文档及作废记录信息列表，错误信息
// 异常：数据库查询失败
// 业务逻辑：
//   - 查询指定主文档的所有作废版本（status=-1）
//   - 联查作废申请表，只查询已审批通过的记录（approval_status=3）
//   - 同时获取每个版本对应的作废记录ID
//   - 按更新时间倒序排列
func (c *ExternalDocumentLibraryClient) GetExternalDeprecatedDocumentVersionsWithRecords(ctx context.Context, mainID string) ([]ExternalDeprecatedDocumentWithRecord, error) {
	var results []ExternalDeprecatedDocumentWithRecord

	// 使用内联查询：external_document_library JOIN deprecation_document_relations JOIN deprecation_records
	query := `
		SELECT
			edl.*,
			ddr.deprecation_record_id
		FROM external_document_library edl
		INNER JOIN deprecation_document_relations ddr ON edl.id = ddr.document_id
		INNER JOIN deprecation_records dr ON ddr.deprecation_record_id = dr.id
		WHERE (edl.main_id = ? OR (edl.main_id = '' AND edl.id = ?))
		  AND edl.status = -1
		  AND dr.approval_status = 3
		ORDER BY edl.updated_at DESC
	`

	if err := c.db.WithContext(ctx).Raw(query, mainID, mainID).Scan(&results).Error; err != nil {
		logc.Error(ctx, "Failed to get external deprecated document versions with records", err)
		return nil, err
	}

	return results, nil
}
