package deprecationrecord

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/query/deprecationrecord/assemblers"
	"nebula/internal/query/deprecationrecord/filters"
	"nebula/internal/query/deprecationrecord/services"
	"nebula/internal/svc"
	"nebula/internal/types"
)

// DeprecationRecordQueryService 作废记录查询服务
// 功能：对外提供作废记录的统一查询服务，封装内部的查询组件
type DeprecationRecordQueryService struct {
	concurrentQueryService     *services.ConcurrentQueryService
	deprecationRecordAssembler *assemblers.DeprecationRecordAssembler
	filterPipeline             *filters.FilterPipeline
	svcCtx                     *svc.ServiceContext
}

// NewDeprecationRecordQueryService 创建作废记录查询服务
// 功能：创建作废记录查询服务实例，初始化所有查询组件
// 参数：svcCtx - 服务上下文
// 返回值：作废记录查询服务实例
func NewDeprecationRecordQueryService(svcCtx *svc.ServiceContext) *DeprecationRecordQueryService {
	return &DeprecationRecordQueryService{
		concurrentQueryService:     services.NewConcurrentQueryService(svcCtx),
		deprecationRecordAssembler: assemblers.NewDeprecationRecordAssembler(svcCtx.QuickNameTranslator),
		filterPipeline:             filters.NewFilterPipeline(),
		svcCtx:                     svcCtx,
	}
}

// GetDeprecateApplications 获取作废申请列表
// 功能：统一的作废申请查询入口，整合所有查询逻辑
// 参数：ctx - 上下文，req - 查询请求
// 返回值：作废申请响应，错误信息
// 异常：参数验证失败、数据库查询失败、数据组装失败
func (drqs *DeprecationRecordQueryService) GetDeprecateApplications(ctx context.Context, req *types.GetDeprecateApplicationsReq) (*types.GetDeprecateApplicationsResp, error) {
	// 实现步骤：
	// 1. 构建分页查询请求
	// 2. 设置过滤器链
	// 3. 应用过滤器
	// 4. 执行数据库查询
	// 5. 并发查询关联数据
	// 6. 组装响应数据

	// 1. 构建分页查询请求
	pageReq := mapper.DeprecationRecordPageReq{
		Page:     int(req.PageInfo.Page),
		PageSize: int(req.PageSize),
		NoPage:   req.NoPage,
	}

	// 2. 设置过滤器管道
	drqs.setupFilterPipeline()

	// 3. 应用过滤器
	isEmpty, err := drqs.filterPipeline.Execute(ctx, req, &pageReq)
	if err != nil {
		return nil, err
	}
	if isEmpty {
		return &types.GetDeprecateApplicationsResp{
			Data:  []types.DeprecateApplication{},
			Total: 0,
		}, nil
	}

	// 4. 执行数据库查询
	deprecationRecordClient := mapper.NewDeprecationRecordClient(drqs.svcCtx.DocvaultDB)
	deprecationRecords, total, err := deprecationRecordClient.GetDeprecationRecords(ctx, pageReq)
	if err != nil {
		return nil, err
	}

	if len(deprecationRecords) == 0 {
		return &types.GetDeprecateApplicationsResp{
			Data:  []types.DeprecateApplication{},
			Total: total,
		}, nil
	}

	// 5. 并发查询关联数据
	queryResult, err := drqs.concurrentQueryService.QueryConcurrently(ctx, deprecationRecords)
	if err != nil {
		return nil, err
	}

	// 6. 组装最终响应数据
	assemblerQueryResult := &assemblers.QueryResult{
		UserNicknames:   queryResult.UserNicknames,
		DocumentCounts:  queryResult.DocumentCounts,
		CategoryNames:   queryResult.CategoryNames,
		DocumentDetails: queryResult.DocumentDetails,
	}

	return drqs.deprecationRecordAssembler.AssembleWithPagination(deprecationRecords, assemblerQueryResult, int(total)), nil
}

// GetDeprecateApplicationDetail 获取作废申请详情
// 功能：查询作废申请的详细信息，包括关联文档列表
// 参数：ctx - 上下文，req - 详情查询请求
// 返回值：作废申请详情响应，错误信息
// 异常：参数验证失败、数据库查询失败、数据组装失败
func (drqs *DeprecationRecordQueryService) GetDeprecateApplicationDetail(ctx context.Context, req *types.GetDeprecateApplicationDetailReq) (*types.GetDeprecateApplicationDetailResp, error) {
	// 实现步骤：
	// 1. 查询作废记录基本信息
	// 2. 查询关联的文档信息
	// 3. 并发查询用户昵称信息
	// 4. 组装响应数据

	// 1. 查询作废记录基本信息
	deprecationRecordClient := mapper.NewDeprecationRecordClient(drqs.svcCtx.DocvaultDB)
	record, err := deprecationRecordClient.GetByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if record == nil {
		return nil, nil
	}

	// 2. 查询关联的文档信息
	documents, err := deprecationRecordClient.GetDocumentsByDeprecationRecordID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 3. 并发查询用户昵称信息
	queryResult, err := drqs.concurrentQueryService.QueryConcurrently(ctx, []mapper.DeprecationRecord{*record})
	if err != nil {
		return nil, err
	}

	// 4. 组装响应数据
	assemblerQueryResult := &assemblers.QueryResult{
		UserNicknames:  queryResult.UserNicknames,
		DocumentCounts: queryResult.DocumentCounts,
		CategoryNames:  queryResult.CategoryNames,
	}

	return drqs.deprecationRecordAssembler.AssembleDetail(record, documents, assemblerQueryResult), nil
}

// setupFilterPipeline 设置过滤器管道
// 功能：配置过滤器管道，使用文档连接优化策略，添加所有需要的过滤器函数
func (drqs *DeprecationRecordQueryService) setupFilterPipeline() {
	// 实现步骤：
	// 1. 清空现有过滤器
	// 2. 按顺序添加过滤器函数

	// 1. 清空现有过滤器
	drqs.filterPipeline.Clear()

	// 2. 按顺序添加过滤器函数
	// 注意：过滤器的添加顺序会影响执行效率，应该将过滤性更强的过滤器放在前面
	// 所有过滤器现在都使用文档连接优化策略，直接在数据库层面进行过滤
	drqs.filterPipeline.
		Add(filters.WithOrganizationFilter(drqs.svcCtx)).     // 组织过滤器（必须）
		Add(filters.WithApprovalStatusFilter()).              // 审批状态过滤器
		Add(filters.WithDocumentModuleFilter(drqs.svcCtx)).   // 文档模块过滤器
		Add(filters.WithApplicantFilter(drqs.svcCtx)).        // 申请人过滤器
		Add(filters.WithDocumentCategoryFilter(drqs.svcCtx)). // 文档类别过滤器
		Add(filters.WithDocumentNoFilter(drqs.svcCtx)).       // 文档编号过滤器
		Add(filters.WithDocumentNameFilter(drqs.svcCtx))      // 文档名称过滤器
}

// GetInternalDeprecatedDocuments 获取内部作废文档列表
// 功能：查询内部作废文档列表，按文档聚合显示作废版本统计
// 参数：ctx - 上下文，req - 查询请求
// 返回值：内部作废文档响应，错误信息
// 异常：参数验证失败、数据库查询失败、数据组装失败
func (drqs *DeprecationRecordQueryService) GetInternalDeprecatedDocuments(ctx context.Context, req *types.GetInternalDeprecatedDocumentsReq) (*types.GetInternalDeprecatedDocumentsResp, error) {
	// 实现步骤：
	// 1. 构建分页查询请求
	// 2. 设置内部文档过滤器链
	// 3. 应用过滤器
	// 4. 执行数据库查询
	// 5. 并发查询关联数据
	// 6. 组装响应数据

	// 1. 构建分页查询请求
	pageReq := mapper.InternalDocumentLibraryPageReq{
		Page:           int(req.PageInfo.Page),
		PageSize:       int(req.PageSize),
		NoPage:         req.NoPage,
		Status:         3,  // 3表示作废状态
		OrganizationID: "", // 将在过滤器中设置
	}

	// 2. 设置内部文档过滤器管道
	drqs.setupInternalDeprecatedDocumentFilterPipeline()

	// 3. 应用过滤器
	isEmpty, err := drqs.filterPipeline.ExecuteInternalDocument(ctx, req, &pageReq)
	if err != nil {
		return nil, err
	}
	if isEmpty {
		return &types.GetInternalDeprecatedDocumentsResp{
			Data:  []types.InternalDeprecatedDocument{},
			Total: 0,
		}, nil
	}

	// 4. 执行数据库查询 - 查询作废状态的文档
	internalDocumentClient := mapper.NewInternalDocumentLibraryClient(drqs.svcCtx.DocvaultDB)
	deprecatedDocuments, total, err := internalDocumentClient.PageDeprecationRecords(ctx, pageReq)
	if err != nil {
		return nil, err
	}

	if len(deprecatedDocuments) == 0 {
		return &types.GetInternalDeprecatedDocumentsResp{
			Data:  []types.InternalDeprecatedDocument{},
			Total: 0,
		}, nil
	}

	// 5. 并发查询关联数据（文档类别名称等）
	queryResult, err := drqs.concurrentQueryService.QueryInternalDocumentsConcurrently(ctx, deprecatedDocuments)
	if err != nil {
		return nil, err
	}

	// 6. 组装最终响应数据
	assemblerQueryResult := &assemblers.InternalDocumentQueryResult{
		CategoryNames:            queryResult.CategoryNames,
		DocumentDateAggregations: queryResult.DocumentDateAggregations,
	}

	return drqs.deprecationRecordAssembler.AssembleInternalDeprecatedDocuments(deprecatedDocuments, assemblerQueryResult, int(total)), nil
}

// GetExternalDeprecatedDocuments 获取外部作废文档列表
// 功能：查询外部作废文档列表，按文档聚合显示作废版本统计
// 参数：ctx - 上下文，req - 查询请求
// 返回值：外部作废文档响应，错误信息
// 异常：参数验证失败、数据库查询失败、数据组装失败
func (drqs *DeprecationRecordQueryService) GetExternalDeprecatedDocuments(ctx context.Context, req *types.GetExternalDeprecatedDocumentsReq) (*types.GetExternalDeprecatedDocumentsResp, error) {
	// 实现步骤：
	// 1. 构建分页查询请求
	// 2. 设置外部文档过滤器链
	// 3. 应用过滤器
	// 4. 执行数据库查询
	// 5. 并发查询关联数据
	// 6. 组装响应数据

	// 1. 构建分页查询请求
	pageReq := mapper.ExternalDocumentPage{
		Page:     int(req.PageInfo.Page),
		PageSize: int(req.PageSize),
		NoPage:   req.NoPage,
	}

	// 2. 设置外部文档过滤器管道
	drqs.setupExternalDeprecatedDocumentFilterPipeline()

	// 3. 应用过滤器
	isEmpty, err := drqs.filterPipeline.ExecuteExternalDocument(ctx, req, &pageReq)
	if err != nil {
		return nil, err
	}
	if isEmpty {
		return &types.GetExternalDeprecatedDocumentsResp{
			Data:  []types.ExternalDeprecatedDocument{},
			Total: 0,
		}, nil
	}

	// 4. 执行数据库查询 - 使用专用的外部库作废记录查询方法
	externalDocumentClient := mapper.NewExternalDocumentLibraryClient(drqs.svcCtx.DocvaultDB)
	deprecatedDocuments, total, err := externalDocumentClient.PageExternalDeprecationRecords(ctx, pageReq)
	if err != nil {
		return nil, err
	}

	if len(deprecatedDocuments) == 0 {
		return &types.GetExternalDeprecatedDocumentsResp{
			Data:  []types.ExternalDeprecatedDocument{},
			Total: total,
		}, nil
	}

	// 5. 并发查询关联数据（文档类别名称等）
	queryResult, err := drqs.concurrentQueryService.QueryExternalDocumentsConcurrently(ctx, deprecatedDocuments)
	if err != nil {
		return nil, err
	}

	// 6. 组装最终响应数据
	assemblerQueryResult := &assemblers.ExternalDocumentQueryResult{
		TypeNames:  queryResult.TypeNames,
		DateRanges: queryResult.DateRanges,
	}

	return drqs.deprecationRecordAssembler.AssembleExternalDeprecatedDocuments(deprecatedDocuments, assemblerQueryResult, int(total)), nil
}

// GetExternalDeprecatedDocumentDetail 获取外部作废文档详情
// 功能：查询外部作废文档的详细信息，包括所有作废版本列表
// 参数：ctx - 上下文，req - 详情查询请求
// 返回值：外部作废文档详情响应，错误信息
// 异常：参数验证失败、数据库查询失败、数据组装失败
func (drqs *DeprecationRecordQueryService) GetExternalDeprecatedDocumentDetail(ctx context.Context, req *types.GetExternalDeprecatedDocumentDetailReq) (*types.GetExternalDeprecatedDocumentDetailResp, error) {
	// 实现步骤：
	// 1. 查询主文档基本信息
	// 2. 使用内联查询同时获取文档库信息和作废记录信息
	// 3. 分离文档版本和作废记录映射
	// 4. 并发查询关联数据（文档类型名称等）
	// 5. 组装响应数据

	// 1. 查询主文档基本信息
	externalDocumentClient := mapper.NewExternalDocumentLibraryClient(drqs.svcCtx.DocvaultDB)
	mainDocument, err := externalDocumentClient.GetByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 2. 使用内联查询同时获取文档库信息和作废记录信息
	// 确定主文档ID
	var searchID string
	if mainDocument.MainID != "" {
		searchID = mainDocument.MainID
	} else {
		searchID = mainDocument.ID
	}

	deprecatedVersionsWithRecords, err := externalDocumentClient.GetExternalDeprecatedDocumentVersionsWithRecords(ctx, searchID)
	if err != nil {
		return nil, err
	}

	// 如果没有找到作废版本，返回空结果（因为该文档没有被作废）
	if len(deprecatedVersionsWithRecords) == 0 {
		return &types.GetExternalDeprecatedDocumentDetailResp{
			ID:             mainDocument.ID,
			Number:         mainDocument.Number,
			Name:           mainDocument.Name,
			DeprecatedList: []types.ExternalDeprecatedVersionDetail{},
		}, nil
	}

	// 3. 分离文档版本和作废记录映射
	deprecatedVersions := make([]mapper.ExternalDocumentLibrary, 0, len(deprecatedVersionsWithRecords))
	documentToDeprecationRecordMap := make(map[string]string)

	for _, item := range deprecatedVersionsWithRecords {
		deprecatedVersions = append(deprecatedVersions, item.ExternalDocumentLibrary)
		documentToDeprecationRecordMap[item.ID] = item.DeprecationRecordID
	}

	// 4. 并发查询关联数据（文档类型名称等）
	queryResult, err := drqs.concurrentQueryService.QueryExternalDocumentsConcurrently(ctx, deprecatedVersions)
	if err != nil {
		return nil, err
	}

	// 5. 组装最终响应数据
	assemblerQueryResult := &assemblers.ExternalDocumentQueryResult{
		TypeNames:                      queryResult.TypeNames,
		DocumentToDeprecationRecordMap: documentToDeprecationRecordMap,
	}

	return drqs.deprecationRecordAssembler.AssembleExternalDeprecatedDocumentDetail(&mainDocument, deprecatedVersions, assemblerQueryResult), nil
}

// GetInternalDeprecatedDocumentDetail 获取内部作废文档详情
// 功能：查询内部作废文档的详细信息，包括所有作废版本列表
// 参数：ctx - 上下文，req - 详情查询请求
// 返回值：内部作废文档详情响应，错误信息
// 异常：参数验证失败、数据库查询失败、数据组装失败
func (drqs *DeprecationRecordQueryService) GetInternalDeprecatedDocumentDetail(ctx context.Context, req *types.GetInternalDeprecatedDocumentDetailReq) (*types.GetInternalDeprecatedDocumentDetailResp, error) {
	// 实现步骤：
	// 1. 查询主文档基本信息
	// 2. 查询该文档的所有作废版本（包括主文档和历史版本）
	// 3. 并发查询关联数据（文档类别名称等）
	// 4. 组装响应数据

	// 1. 查询主文档基本信息
	internalDocumentClient := mapper.NewInternalDocumentLibraryClient(drqs.svcCtx.DocvaultDB)
	mainDocument, err := internalDocumentClient.GetByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 2. 使用内联查询同时获取文档库信息和作废记录信息
	// 确定主文档ID
	var searchID string
	if mainDocument.MainID != "" {
		searchID = mainDocument.MainID
	} else {
		searchID = mainDocument.ID
	}

	deprecatedVersionsWithRecords, err := internalDocumentClient.GetDeprecatedDocumentVersionsWithRecords(ctx, searchID)
	if err != nil {
		return nil, err
	}

	// 如果没有找到作废版本，返回空结果（因为该文档没有被作废）
	if len(deprecatedVersionsWithRecords) == 0 {
		return &types.GetInternalDeprecatedDocumentDetailResp{
			ID:             mainDocument.ID,
			DocumentNo:     mainDocument.No,
			DocumentName:   mainDocument.Name,
			DeprecatedList: []types.InternalDeprecatedVersionDetail{},
		}, nil
	}

	// 提取文档版本信息和作废记录映射
	deprecatedVersions := make([]mapper.InternalDocumentLibrary, 0, len(deprecatedVersionsWithRecords))
	documentToDeprecationRecordMap := make(map[string]string)

	for _, item := range deprecatedVersionsWithRecords {
		deprecatedVersions = append(deprecatedVersions, item.InternalDocumentLibrary)
		documentToDeprecationRecordMap[item.InternalDocumentLibrary.ID] = item.DeprecationRecordID
	}

	// 4. 并发查询关联数据（文档类别名称等）- 使用专门的详情查询方法避免重复查询
	queryResult, err := drqs.concurrentQueryService.QueryInternalDocumentsConcurrentlyForDetail(ctx, deprecatedVersions)
	if err != nil {
		return nil, err
	}

	// 6. 组装最终响应数据
	assemblerQueryResult := &assemblers.InternalDocumentQueryResult{
		CategoryNames:                  queryResult.CategoryNames,
		DocumentDateAggregations:       queryResult.DocumentDateAggregations,
		DocumentToDeprecationRecordMap: documentToDeprecationRecordMap,
	}

	return drqs.deprecationRecordAssembler.AssembleInternalDeprecatedDocumentDetail(ctx, &mainDocument, deprecatedVersions, assemblerQueryResult), nil
}

// setupExternalDeprecatedDocumentFilterPipeline 设置外部作废文档过滤器管道
// 功能：配置外部作废文档过滤器管道，添加所有需要的过滤器函数
func (drqs *DeprecationRecordQueryService) setupExternalDeprecatedDocumentFilterPipeline() {
	// 实现步骤：
	// 1. 清空现有过滤器
	// 2. 按顺序添加过滤器函数

	// 1. 清空现有过滤器
	drqs.filterPipeline.Clear()

	// 2. 按顺序添加过滤器函数
	drqs.filterPipeline.
		AddExternalDocument(filters.WithOrganizationFilterForExternalDoc(drqs.svcCtx)). // 组织过滤器（必须）
		AddExternalDocument(filters.WithDocumentNoFilterForExternalDoc()).              // 文档编号过滤器
		AddExternalDocument(filters.WithDocumentNameFilterForExternalDoc()).            // 文档名称过滤器
		AddExternalDocument(filters.WithOriginalDocumentNoFilterForExternalDoc()).      // 原文档编号过滤器
		AddExternalDocument(filters.WithDocumentTypeFilterForExternalDoc(drqs.svcCtx)). // 文档类型过滤器
		AddExternalDocument(filters.WithPublishDepartmentFilterForExternalDoc()).       // 发文部门过滤器
		AddExternalDocument(filters.WithStatusFilterForExternalDoc()).                  // 状态过滤器
		AddExternalDocument(filters.WithAttachmentFilterForExternalDoc()).              // 附件过滤器
		AddExternalDocument(filters.WithOrgTypeFilterForExternalDoc())                  // 机构类型过滤器
}

// setupInternalDeprecatedDocumentFilterPipeline 设置内部作废文档过滤器管道
// 功能：配置内部作废文档过滤器管道，添加所有需要的过滤器函数
func (drqs *DeprecationRecordQueryService) setupInternalDeprecatedDocumentFilterPipeline() {
	// 实现步骤：
	// 1. 清空现有过滤器
	// 2. 按顺序添加过滤器函数

	// 1. 清空现有过滤器
	drqs.filterPipeline.Clear()

	// 2. 按顺序添加过滤器函数
	drqs.filterPipeline.
		AddInternalDocument(filters.WithOrganizationFilterForInternalDoc(drqs.svcCtx)).       // 组织过滤器（必须）
		AddInternalDocument(filters.WithDocumentNoFilterForInternalDoc()).                    // 文档编号过滤器
		AddInternalDocument(filters.WithDocumentNameFilterForInternalDoc()).                  // 文档名称过滤器
		AddInternalDocument(filters.WithOriginalDocumentNoFilterForInternalDoc()).            // 原文档编号过滤器
		AddInternalDocument(filters.WithDocumentCategoryFilterForInternalDoc(drqs.svcCtx)).   // 文档类别过滤器
		AddInternalDocument(filters.WithDraftingDepartmentFilterForInternalDoc(drqs.svcCtx)). // 编制部门过滤器
		AddInternalDocument(filters.WithStatusFilterForInternalDoc()).                        // 状态过滤器
		AddInternalDocument(filters.WithAttachmentFilterForInternalDoc())                     // 附件过滤器
}

// QueryConcurrently 并发查询
// 功能：对外提供并发查询接口
// 参数：ctx - 上下文，deprecationRecords - 作废记录列表
// 返回值：查询结果，错误信息
func (drqs *DeprecationRecordQueryService) QueryConcurrently(ctx context.Context, deprecationRecords []mapper.DeprecationRecord) (*services.QueryResult, error) {
	return drqs.concurrentQueryService.QueryConcurrently(ctx, deprecationRecords)
}
