package assemblers

import (
	"context"
	"encoding/json"
	"strings"

	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/types"
)

// DeprecationRecordAssembler 作废记录组装器
// 功能：将数据库查询结果组装成前端需要的数据格式
type DeprecationRecordAssembler struct {
	quickNameTranslator addons.QuickNameTranslator
}

// NewDeprecationRecordAssembler 创建作废记录组装器
// 功能：创建作废记录组装器实例
// 参数：quickNameTranslator - 快速名称翻译器
// 返回值：作废记录组装器实例
func NewDeprecationRecordAssembler(quickNameTranslator addons.QuickNameTranslator) *DeprecationRecordAssembler {
	return &DeprecationRecordAssembler{
		quickNameTranslator: quickNameTranslator,
	}
}

// translateCommaDelimitedIDs 翻译英文逗号分割的ID字符串
// 功能：将英文逗号分割的ID字符串翻译为中文名称，并用中文顿号连接
// 参数：ctx - 上下文，idsStr - 英文逗号分割的ID字符串，translateFunc - 翻译函数
// 返回值：翻译后的中文名称字符串，用中文顿号（、）连接
func (assembler *DeprecationRecordAssembler) translateCommaDelimitedIDs(ctx context.Context, idsStr string, translateFunc func(context.Context, string) string) string {
	if idsStr == "" {
		return ""
	}

	// 按英文逗号分割
	ids := strings.Split(idsStr, ",")
	translatedNames := make([]string, 0, len(ids))

	// 翻译每个ID
	for _, id := range ids {
		id = strings.TrimSpace(id) // 去除空格
		if id != "" {
			translatedName := translateFunc(ctx, id)
			if translatedName != "" {
				translatedNames = append(translatedNames, translatedName)
			} else {
				// 如果翻译失败，使用原ID
				translatedNames = append(translatedNames, id)
			}
		}
	}

	// 用中文顿号连接
	return strings.Join(translatedNames, "、")
}

// translateUserIDs 翻译用户ID字符串
// 功能：将英文逗号分割的用户ID字符串翻译为用户昵称
// 参数：ctx - 上下文，userIDs - 英文逗号分割的用户ID字符串
// 返回值：翻译后的用户昵称字符串，用中文顿号（、）连接
func (assembler *DeprecationRecordAssembler) translateUserIDs(ctx context.Context, userIDs string) string {
	return assembler.translateCommaDelimitedIDs(ctx, userIDs, assembler.quickNameTranslator.TranslateUserNickname)
}

// translateOrganizationIDs 翻译组织ID字符串
// 功能：将英文逗号分割的组织ID字符串翻译为组织名称
// 参数：ctx - 上下文，orgIDs - 英文逗号分割的组织ID字符串
// 返回值：翻译后的组织名称字符串，用中文顿号（、）连接
func (assembler *DeprecationRecordAssembler) translateOrganizationIDs(ctx context.Context, orgIDs string) string {
	return assembler.translateCommaDelimitedIDs(ctx, orgIDs, assembler.quickNameTranslator.TranslateOrganizationName)
}

// AssembleWithPagination 组装分页查询结果
// 功能：将数据库查询结果和关联数据组装成前端需要的分页响应格式
// 参数：deprecationRecords - 作废记录列表，queryResult - 查询结果，total - 总数
// 返回值：作废申请响应对象
func (assembler *DeprecationRecordAssembler) AssembleWithPagination(
	deprecationRecords []mapper.DeprecationRecord,
	queryResult *QueryResult,
	total int,
) *types.GetDeprecateApplicationsResp {
	// 实现步骤：
	// 1. 初始化响应对象
	// 2. 遍历作废记录，为每个记录组装数据
	// 3. 设置总数并返回响应

	// 1. 初始化响应对象
	applications := make([]types.DeprecateApplication, 0, len(deprecationRecords))

	// 2. 遍历作废记录，为每个记录组装数据
	for _, record := range deprecationRecords {
		application := assembler.assembleDeprecateApplication(record, queryResult)
		applications = append(applications, application)
	}

	// 3. 设置总数并返回响应
	return &types.GetDeprecateApplicationsResp{
		Total: int64(total),
		Data:  applications,
	}
}

// assembleDeprecateApplication 组装单个作废申请数据
// 功能：将单个作废记录和关联数据组装成前端需要的格式
// 参数：record - 作废记录，queryResult - 查询结果
// 返回值：作废申请对象
func (assembler *DeprecationRecordAssembler) assembleDeprecateApplication(
	record mapper.DeprecationRecord,
	queryResult *QueryResult,
) types.DeprecateApplication {
	// 实现步骤：
	// 1. 创建基础的作废申请对象
	// 2. 设置用户昵称信息
	// 3. 设置文档数量信息
	// 4. 处理审批信息
	// 5. 返回组装后的对象

	// 1. 创建基础的作废申请对象
	application := types.DeprecateApplication{
		ID:                   record.ID,
		PlannedDeprecateDate: record.DeprecateAt.UnixMilli(),
		ApplyDate:            record.CreatedAt.UnixMilli(),
		Status:               record.ApprovalStatus,
		WorkflowID:           record.WorkflowID,
	}

	// 2. 设置用户昵称信息
	if applicantNickname, exists := queryResult.UserNicknames[record.CreatedBy]; exists {
		application.Applicant = applicantNickname
	} else {
		application.Applicant = record.CreatedBy // 如果找不到昵称，使用用户ID
	}

	// 3. 设置文档数量信息
	if documentCount, exists := queryResult.DocumentCounts[record.ID]; exists {
		application.DeprecateDocumentCount = documentCount
	} else {
		application.DeprecateDocumentCount = 0
	}

	// 4. 处理审批信息
	if len(record.ApprovalInfo) > 0 {
		var approvalInfo types.ApprovalInfo
		if err := json.Unmarshal(record.ApprovalInfo, &approvalInfo); err == nil {
			application.ApprovalInfo = approvalInfo
		}
	}
	if r, exists := queryResult.DocumentDetails[record.ID]; exists {
		if len(r) > 0 {
			application.DocumentModuleType = int32(r[0].DocumentModule)
			if categoryName, exists := queryResult.CategoryNames[r[0].DocumentCategoryID]; exists {
				application.DocumentCategoryName = categoryName
			} else {
				application.DocumentCategoryName = ""
			}
		}
	}

	// 5. 返回组装后的对象
	return application
}

// AssembleDetail 组装详情数据
// 功能：将作废记录详情数据组装成前端需要的格式
// 参数：record - 作废记录，documents - 关联文档列表，queryResult - 查询结果
// 返回值：作废申请详情响应
func (assembler *DeprecationRecordAssembler) AssembleDetail(
	record *mapper.DeprecationRecord,
	documents []mapper.DeprecationDocumentInfo,
	queryResult *QueryResult,
) *types.GetDeprecateApplicationDetailResp {
	// 实现步骤：
	// 1. 组装基础的作废申请信息
	// 2. 组装文档列表信息
	// 3. 设置文档模块类型和类别名称
	// 4. 返回详情响应

	// 1. 组装基础的作废申请信息
	application := assembler.assembleDeprecateApplication(*record, queryResult)

	// 2. 组装文档列表信息
	documentList := make([]types.DeprecateDocumentItem, 0, len(documents))
	for _, doc := range documents {
		documentList = append(documentList, types.DeprecateDocumentItem{
			DocumentID:        doc.DocumentID,
			DocumentName:      doc.DocumentName,
			DocumentNo:        doc.DocumentNo,
			DocumentVersionNo: doc.DocumentVersionNo,
		})
	}

	// 3. 设置文档模块类型和类别名称（从第一个文档获取）
	if len(documents) > 0 {
		application.DocumentModuleType = int32(documents[0].DocumentModule)
		// 根据文档类别ID查询类别名称
		if categoryName, exists := queryResult.CategoryNames[documents[0].DocumentCategoryID]; exists {
			application.DocumentCategoryName = categoryName
		} else {
			application.DocumentCategoryName = ""
		}
	}

	// 4. 返回详情响应
	return &types.GetDeprecateApplicationDetailResp{
		ID:                   record.ID,
		Applicant:            application.Applicant,
		ApplyDate:            application.ApplyDate,
		PlannedDeprecateDate: application.PlannedDeprecateDate,
		DocumentModuleType:   application.DocumentModuleType,
		DocumentCategoryName: application.DocumentCategoryName,
		DocumentCategoryID:   documents[0].DocumentCategoryID,
		DeprecateReason:      record.Reason,
		OtherReason:          record.OtherReason,
		DeprecateList:        documentList,
		ApprovalInfo:         application.ApprovalInfo,
		Status:               application.Status,
		WorkflowID:           application.WorkflowID,
	}
}

// ==================== 内部文档组装方法 ====================

// AssembleInternalDeprecatedDocuments 组装内部作废文档分页查询结果
// 功能：将内部文档库查询结果和关联数据组装成前端需要的分页响应格式
// 参数：deprecatedDocuments - 内部文档库列表，queryResult - 查询结果，total - 总数
// 返回值：内部作废文档响应对象
func (assembler *DeprecationRecordAssembler) AssembleInternalDeprecatedDocuments(
	deprecatedDocuments []mapper.InternalDocumentLibrary,
	queryResult *InternalDocumentQueryResult,
	total int,
) *types.GetInternalDeprecatedDocumentsResp {
	// 实现步骤：
	// 1. 初始化响应对象
	// 2. 遍历作废文档，为每个文档组装数据
	// 3. 设置总数并返回响应

	// 1. 初始化响应对象
	documents := make([]types.InternalDeprecatedDocument, 0, len(deprecatedDocuments))

	// 2. 遍历作废文档，为每个文档组装数据
	for _, document := range deprecatedDocuments {
		deprecatedDocument := assembler.assembleInternalDeprecatedDocument(document, queryResult)
		documents = append(documents, deprecatedDocument)
	}

	// 3. 设置总数并返回响应
	return &types.GetInternalDeprecatedDocumentsResp{
		Total: int64(total),
		Data:  documents,
	}
}

// assembleInternalDeprecatedDocument 组装单个内部作废文档数据
// 功能：将单个内部文档库记录和关联数据组装成前端需要的格式
// 参数：document - 内部文档库记录，queryResult - 查询结果
// 返回值：内部作废文档对象
func (assembler *DeprecationRecordAssembler) assembleInternalDeprecatedDocument(
	document mapper.InternalDocumentLibrary,
	queryResult *InternalDocumentQueryResult,
) types.InternalDeprecatedDocument {
	// 实现步骤：
	// 1. 创建基础的内部作废文档对象
	// 2. 设置文档类别名称
	// 3. 处理时间字段转换（使用作废记录的聚合日期）
	// 4. 返回组装后的对象

	// 1. 创建基础的内部作废文档对象
	deprecatedDocument := types.InternalDeprecatedDocument{
		ID:                     document.ID,
		DocumentNo:             document.No,
		DocumentName:           document.Name,
		DeprecatedVersionCount: 1, // 暂时设为1，后续可以根据需要计算实际作废版本数
	}

	// 2. 设置文档类别名称
	if categoryName, exists := queryResult.CategoryNames[document.DocCategoryID]; exists {
		deprecatedDocument.DocumentCategoryName = categoryName
	} else {
		deprecatedDocument.DocumentCategoryName = ""
	}

	// 3. 处理时间字段转换
	// 统一使用文档库的聚合日期（从internal_document_library表查询）
	if aggregation, exists := queryResult.DocumentDateAggregations[document.ID]; exists {
		// 使用文档库的聚合日期
		deprecatedDocument.FirstPublishDate = aggregation.FirstPublishDate.UnixMilli()     // 最早发布日期（publish_date）
		deprecatedDocument.FirstImplementDate = aggregation.FirstEffectiveDate.UnixMilli() // 最早实施日期（effective_date）
		deprecatedDocument.LastDeprecatedDate = aggregation.LastDeprecatedDate.UnixMilli() // 最晚作废日期（updated_at）
	} else {
		// 如果没有聚合数据，使用当前文档的日期作为默认值
		deprecatedDocument.FirstPublishDate = document.PublishDate.UnixMilli()
		deprecatedDocument.FirstImplementDate = document.EffectiveDate.UnixMilli()
		deprecatedDocument.LastDeprecatedDate = document.UpdatedAt.UnixMilli()
	}

	// 4. 返回组装后的对象
	return deprecatedDocument
}

// AssembleInternalDeprecatedDocumentDetail 组装内部作废文档详情响应
// 功能：将内部文档库记录和版本列表组装成详情响应格式
// 参数：ctx - 上下文，mainDocument - 主文档记录，deprecatedVersions - 作废版本列表，queryResult - 查询结果
// 返回值：内部作废文档详情响应对象
func (assembler *DeprecationRecordAssembler) AssembleInternalDeprecatedDocumentDetail(
	ctx context.Context,
	mainDocument *mapper.InternalDocumentLibrary,
	deprecatedVersions []mapper.InternalDocumentLibrary,
	queryResult *InternalDocumentQueryResult,
) *types.GetInternalDeprecatedDocumentDetailResp {
	// 实现步骤：
	// 1. 创建基础的详情响应对象
	// 2. 设置文档类别名称
	// 3. 处理时间字段转换
	// 4. 组装作废版本详情列表
	// 5. 返回组装后的对象

	// 1. 创建基础的详情响应对象
	detail := &types.GetInternalDeprecatedDocumentDetailResp{
		ID:           mainDocument.ID,
		DocumentNo:   mainDocument.No,
		DocumentName: mainDocument.Name,
	}

	// 2. 设置文档类别名称
	if categoryName, exists := queryResult.CategoryNames[mainDocument.DocCategoryID]; exists {
		detail.DocumentCategoryName = categoryName
	} else {
		detail.DocumentCategoryName = ""
	}

	// 3. 处理时间字段转换 - 直接从所有版本中计算聚合日期
	assembler.calculateAggregatedTimestampsForInternalDocument(detail, deprecatedVersions)

	// 4. 组装作废版本详情列表
	deprecatedList := make([]types.InternalDeprecatedVersionDetail, 0, len(deprecatedVersions))
	for _, version := range deprecatedVersions {
		versionDetail := assembler.assembleInternalDeprecatedVersionDetail(ctx, version, queryResult)
		deprecatedList = append(deprecatedList, versionDetail)
	}
	detail.DeprecatedList = deprecatedList

	// 5. 返回组装后的对象
	return detail
}

// ==================== 外部文档组装方法 ====================

// AssembleExternalDeprecatedDocuments 组装外部作废文档分页查询结果
// 功能：将外部文档库查询结果和关联数据组装成前端需要的分页响应格式
// 参数：deprecatedDocuments - 外部文档库列表，queryResult - 查询结果，total - 总数
// 返回值：外部作废文档响应对象
func (assembler *DeprecationRecordAssembler) AssembleExternalDeprecatedDocuments(
	deprecatedDocuments []mapper.ExternalDocumentLibrary,
	queryResult *ExternalDocumentQueryResult,
	total int,
) *types.GetExternalDeprecatedDocumentsResp {
	// 实现步骤：
	// 1. 初始化响应对象
	// 2. 遍历作废文档，为每个文档组装数据
	// 3. 设置总数并返回响应

	// 1. 初始化响应对象
	documents := make([]types.ExternalDeprecatedDocument, 0, len(deprecatedDocuments))

	// 2. 遍历作废文档，为每个文档组装数据
	for _, document := range deprecatedDocuments {
		deprecatedDocument := assembler.assembleExternalDeprecatedDocument(document, queryResult)
		documents = append(documents, deprecatedDocument)
	}

	// 3. 设置总数并返回响应
	return &types.GetExternalDeprecatedDocumentsResp{
		Total: int64(total),
		Data:  documents,
	}
}

// assembleExternalDeprecatedDocument 组装单个外部作废文档数据
// 功能：将单个外部文档库记录和关联数据组装成前端需要的格式
// 参数：document - 外部文档库记录，queryResult - 查询结果
// 返回值：外部作废文档对象
func (assembler *DeprecationRecordAssembler) assembleExternalDeprecatedDocument(
	document mapper.ExternalDocumentLibrary,
	queryResult *ExternalDocumentQueryResult,
) types.ExternalDeprecatedDocument {
	// 实现步骤：
	// 1. 创建基础的外部作废文档对象
	// 2. 设置文档类型名称
	// 3. 处理时间字段转换（使用作废记录的聚合日期）
	// 4. 返回组装后的对象

	// 1. 创建基础的外部作废文档对象
	deprecatedDocument := types.ExternalDeprecatedDocument{
		ID:                     document.ID,
		Number:                 document.Number,
		Name:                   document.Name,
		DeprecatedVersionCount: 1, // 暂时设为1，后续可以根据需要计算实际作废版本数
	}

	// 2. 设置文档类型名称
	if typeName, exists := queryResult.TypeNames[document.TypeDictionaryNodeId]; exists {
		deprecatedDocument.DocType = typeName
	} else {
		deprecatedDocument.DocType = document.DocType // 如果找不到类型名称，使用原有字段
	}

	// 3. 处理时间字段转换（使用文档库的聚合日期）
	if dateRange, exists := queryResult.DateRanges[document.ID]; exists {
		// 使用文档库的聚合日期（从external_document_library表查询）
		// 注意：DateRanges现在包含的是从文档库聚合的日期信息
		// EarliestCreatedAt 现在对应 FirstPublishDate（最早发布日期）
		// LatestUpdatedAt 现在对应 LastDeprecatedDate（最后作废日期）
		deprecatedDocument.FirstPublishDate = dateRange.EarliestCreatedAt.UnixMilli() // 最早发布日期（publish_date）
		deprecatedDocument.LastDeprecatedDate = dateRange.LatestUpdatedAt.UnixMilli() // 最晚作废日期（updated_at）
		deprecatedDocument.FirstEffectiveDate = document.EffectiveDate.UnixMilli()    // 使用当前文档的生效日期
	} else {
		// 如果没有聚合数据，使用文档本身的日期作为默认值
		deprecatedDocument.FirstPublishDate = document.PublishDate.UnixMilli()
		deprecatedDocument.FirstEffectiveDate = document.EffectiveDate.UnixMilli()
		deprecatedDocument.LastDeprecatedDate = document.UpdatedAt.UnixMilli()
	}

	// 4. 返回组装后的对象
	return deprecatedDocument
}

// AssembleExternalDeprecatedDocumentDetail 组装外部作废文档详情响应
// 功能：将外部文档库记录和版本列表组装成详情响应格式
// 参数：mainDocument - 主文档记录，deprecatedVersions - 作废版本列表，queryResult - 查询结果
// 返回值：外部作废文档详情响应对象
func (assembler *DeprecationRecordAssembler) AssembleExternalDeprecatedDocumentDetail(
	mainDocument *mapper.ExternalDocumentLibrary,
	deprecatedVersions []mapper.ExternalDocumentLibrary,
	queryResult *ExternalDocumentQueryResult,
) *types.GetExternalDeprecatedDocumentDetailResp {
	// 实现步骤：
	// 1. 创建基础的详情响应对象
	// 2. 设置文档类型名称
	// 3. 处理时间字段转换
	// 4. 组装作废版本详情列表
	// 5. 返回组装后的对象

	// 1. 创建基础的详情响应对象
	detail := &types.GetExternalDeprecatedDocumentDetailResp{
		ID:     mainDocument.ID,
		Number: mainDocument.Number,
		Name:   mainDocument.Name,
	}

	// 2. 设置文档类型名称
	if typeName, exists := queryResult.TypeNames[mainDocument.TypeDictionaryNodeId]; exists {
		detail.DocType = typeName
	} else {
		detail.DocType = mainDocument.DocType // 如果找不到类型名称，使用原有字段
	}

	// 3. 处理时间字段转换 - 直接从所有版本中计算聚合日期
	assembler.calculateAggregatedTimestampsForExternalDocument(detail, deprecatedVersions)

	// 4. 组装作废版本详情列表
	deprecatedList := make([]types.ExternalDeprecatedVersionDetail, 0, len(deprecatedVersions))
	for _, version := range deprecatedVersions {
		versionDetail := assembler.assembleExternalDeprecatedVersionDetail(version, queryResult)
		deprecatedList = append(deprecatedList, versionDetail)
	}
	detail.DeprecatedList = deprecatedList

	// 5. 返回组装后的对象
	return detail
}

// assembleExternalDeprecatedVersionDetail 组装单个外部作废版本详情
// 功能：将单个外部文档库记录组装成版本详情格式
// 参数：version - 版本记录，queryResult - 查询结果
// 返回值：外部作废版本详情对象
func (assembler *DeprecationRecordAssembler) assembleExternalDeprecatedVersionDetail(
	version mapper.ExternalDocumentLibrary,
	queryResult *ExternalDocumentQueryResult,
) types.ExternalDeprecatedVersionDetail {
	// 实现步骤：
	// 1. 创建基础的版本详情对象
	// 2. 设置文档类型名称
	// 3. 设置作废记录ID（ApprovalID）
	// 4. 返回组装后的对象

	// 1. 创建基础的版本详情对象
	versionDetail := types.ExternalDeprecatedVersionDetail{
		ID:                    version.ID,
		Number:                version.Number,
		Version:               version.Version,
		OriginalNumber:        version.OriginalNumber,
		OriginalVersion:       version.OriginalVersion,
		Name:                  version.Name,
		EnglishName:           version.EnglishName,
		Domain:                version.Domain,
		OriginalDocNumber:     version.OriginalDocNumber,
		PublishDocNumber:      version.PublishDocNumber,
		PublishDepartment:     version.PublishDepartment,
		ReplacementDocName:    version.ReplacementDocName,
		ReplacementDocVersion: version.ReplacementDocVersion,
		Remark:                version.Remark,
		PublishDate:           version.PublishDate.UnixMilli(),
		EffectiveDate:         version.EffectiveDate.UnixMilli(),
	}

	// 2. 设置文档类型名称
	if typeName, exists := queryResult.TypeNames[version.TypeDictionaryNodeId]; exists {
		versionDetail.DocType = typeName
	} else {
		versionDetail.DocType = version.DocType // 如果找不到类型名称，使用原有字段
	}

	// 3. 设置作废记录ID（ApprovalID）
	if approvalID, exists := queryResult.DocumentToDeprecationRecordMap[version.ID]; exists {
		versionDetail.ApprovalID = approvalID
	} else {
		versionDetail.ApprovalID = ""
	}

	// 4. 返回组装后的对象
	return versionDetail
}

// adjustTimestampsForMultipleVersions 调整多版本情况下的时间戳
// 功能：当有多个版本时，调整首次发布、首次生效和最后作废时间
// 参数：detail - 详情响应对象指针，versions - 版本列表
func (assembler *DeprecationRecordAssembler) adjustTimestampsForMultipleVersions(
	detail *types.GetExternalDeprecatedDocumentDetailResp,
	versions []mapper.ExternalDocumentLibrary,
) {
	if len(versions) == 0 {
		return
	}

	// 查找最早的发布日期和生效日期
	earliestPublish := versions[0].PublishDate
	earliestEffective := versions[0].EffectiveDate
	latestDeprecated := versions[0].UpdatedAt

	for _, version := range versions {
		// 找最早的发布日期
		if version.PublishDate.Before(earliestPublish) {
			earliestPublish = version.PublishDate
		}
		// 找最早的生效日期
		if version.EffectiveDate.Before(earliestEffective) {
			earliestEffective = version.EffectiveDate
		}
		// 找最晚的作废日期（更新时间）
		if version.UpdatedAt.After(latestDeprecated) {
			latestDeprecated = version.UpdatedAt
		}
	}

	// 更新时间戳
	detail.FirstPublishDate = earliestPublish.UnixMilli()
	detail.FirstEffectiveDate = earliestEffective.UnixMilli()
	detail.LastDeprecatedDate = latestDeprecated.UnixMilli()
}

// ==================== 内部文档详情组装辅助方法 ====================

// assembleInternalDeprecatedVersionDetail 组装单个内部作废版本详情
// 功能：将单个内部文档库记录组装成版本详情格式
// 参数：ctx - 上下文，version - 版本记录，queryResult - 查询结果
// 返回值：内部作废版本详情对象
func (assembler *DeprecationRecordAssembler) assembleInternalDeprecatedVersionDetail(
	ctx context.Context,
	version mapper.InternalDocumentLibrary,
	queryResult *InternalDocumentQueryResult,
) types.InternalDeprecatedVersionDetail {
	// 实现步骤：
	// 1. 创建基础的版本详情对象
	// 2. 设置文档类别名称
	// 3. 返回组装后的对象

	// 1. 创建基础的版本详情对象
	versionDetail := types.InternalDeprecatedVersionDetail{
		ID:                     version.ID,
		DocumentNo:             version.No,
		DocumentVersionNo:      version.Version,
		OriginalDocumentNo:     version.OriginalNo,
		OriginalVersionNo:      version.OriginalVersionNo,
		DocumentName:           version.Name,
		DocumentEnglishName:    version.EnglishName,
		DraftingDepartmentName: assembler.translateOrganizationIDs(ctx, version.DepartmentIDs), // 翻译编制部门ID为名称
		Drafter:                assembler.translateUserIDs(ctx, version.AuthorIDs),             // 翻译编制人ID为昵称
		ReplacedDocumentName:   version.ReplacementDocName,
		ReplacedVersionNo:      version.ReplacementDocVersion,
		PublishDate:            version.PublishDate.UnixMilli(),
		ImplementDate:          version.EffectiveDate.UnixMilli(),
		Remark:                 version.Remark,
	}

	// 2. 设置文档类别名称
	if categoryName, exists := queryResult.CategoryNames[version.DocCategoryID]; exists {
		versionDetail.DocumentCategoryName = categoryName
	} else {
		versionDetail.DocumentCategoryName = ""
	}

	// 3. 设置作废记录ID（ApprovalID）
	if approvalID, exists := queryResult.DocumentToDeprecationRecordMap[version.ID]; exists {
		versionDetail.ApprovalID = approvalID
	} else {
		versionDetail.ApprovalID = ""
	}

	// 4. 返回组装后的对象
	return versionDetail
}

// calculateAggregatedTimestampsForInternalDocument 计算内部文档的聚合时间戳
// 功能：从所有版本中计算首次发布、首次生效和最后作废时间
// 参数：detail - 详情响应对象指针，versions - 版本列表
func (assembler *DeprecationRecordAssembler) calculateAggregatedTimestampsForInternalDocument(
	detail *types.GetInternalDeprecatedDocumentDetailResp,
	versions []mapper.InternalDocumentLibrary,
) {
	if len(versions) == 0 {
		return
	}

	// 初始化为第一个版本的日期
	earliestPublish := versions[0].PublishDate
	earliestEffective := versions[0].EffectiveDate
	latestDeprecated := versions[0].UpdatedAt

	// 遍历所有版本，找到最早和最晚的日期
	for _, version := range versions {
		// 找最早的发布日期
		if version.PublishDate.Before(earliestPublish) {
			earliestPublish = version.PublishDate
		}
		// 找最早的生效日期
		if version.EffectiveDate.Before(earliestEffective) {
			earliestEffective = version.EffectiveDate
		}
		// 找最晚的作废日期（更新时间）
		if version.UpdatedAt.After(latestDeprecated) {
			latestDeprecated = version.UpdatedAt
		}
	}

	// 设置聚合后的时间戳
	detail.FirstPublishDate = earliestPublish.UnixMilli()
	detail.FirstImplementDate = earliestEffective.UnixMilli()
	detail.LastDeprecatedDate = latestDeprecated.UnixMilli()
}

// calculateAggregatedTimestampsForExternalDocument 计算外部文档的聚合时间戳
// 功能：从所有版本中计算首次发布、首次生效和最后作废时间
// 参数：detail - 详情响应对象指针，versions - 版本列表
func (assembler *DeprecationRecordAssembler) calculateAggregatedTimestampsForExternalDocument(
	detail *types.GetExternalDeprecatedDocumentDetailResp,
	versions []mapper.ExternalDocumentLibrary,
) {
	if len(versions) == 0 {
		return
	}

	// 初始化为第一个版本的日期
	earliestPublish := versions[0].PublishDate
	earliestEffective := versions[0].EffectiveDate
	latestDeprecated := versions[0].UpdatedAt

	// 遍历所有版本，找到最早和最晚的日期
	for _, version := range versions {
		// 找最早的发布日期
		if version.PublishDate.Before(earliestPublish) {
			earliestPublish = version.PublishDate
		}
		// 找最早的生效日期
		if version.EffectiveDate.Before(earliestEffective) {
			earliestEffective = version.EffectiveDate
		}
		// 找最晚的作废日期（更新时间）
		if version.UpdatedAt.After(latestDeprecated) {
			latestDeprecated = version.UpdatedAt
		}
	}

	// 设置聚合后的时间戳
	detail.FirstPublishDate = earliestPublish.UnixMilli()
	detail.FirstEffectiveDate = earliestEffective.UnixMilli()
	detail.LastDeprecatedDate = latestDeprecated.UnixMilli()
}
